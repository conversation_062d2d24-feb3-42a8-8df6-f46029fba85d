<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <script>(function () {
        var hasSendRequest = false;
        function sendSimpleIndependentRequestOnce(path) {
            if (hasSendRequest) {
                return;
            }
            hasSendRequest = true;
            sendSimpleRequest('https://ti.qq.com/public-template/pv/ptlogin/' + path);
        }
        function sendSimpleRequest(url) {
            var img = new Image();
            img.width = 1;
            img.height = 1;
            var cacheBuster = new Date().getTime();
            var requestUrl = url + (url.indexOf('?') > -1 ? '&' : '?') + '_t=' + cacheBuster;
            img.src = requestUrl;
        }
        window.sendSimpleIndependentRequestOnce = sendSimpleIndependentRequestOnce;
        if (window.location.href.indexOf('force_qr=1') > 0) {
            sendSimpleRequest('https://ti.qq.com/public-template/pv/ptlogin/pv');
            setTimeout(function () {
                window.sendSimpleIndependentRequestOnce('qrcode/timeout');
            }, 5000)        
        }        
    })()</script><!--[if IE]>
<script type="text/javascript">
    window.Aegis = null;// 待兼容
</script>
<![endif]--><!--[if !(IE)]><!--><script>if(void 0===Set||"function"!=typeof Set.prototype.keys)var Set=function(){"use strict";var t={"[object Array]":!0,"[object Arguments]":!0,"[object HTMLCollection]":!0,"[object NodeList]":!0},e=Object.prototype.hasOwnProperty,n=Object.prototype.toString;function r(t,n){return e.call(t,n)}var i=Object.defineProperty&&Object.defineProperties;function o(t,e,n,r,o){i?Object.defineProperty(t,e,{enumerable:r,configurable:!1,writable:o,value:n}):t[e]=n}var a=!1;function u(t,e){a=!0,t.size=e,a=!1}function s(e){var r,u,s=0;if(o(this,"baseType","Set",!1,!1),o(this,"_data",{},!1,!0),i?Object.defineProperty(this,"size",{enumerable:!0,configurable:!1,get:function(){return s},set:function(t){if(!a)throw new Error("Can't set size property on Set object.");s=t}}):this.size=0,null!=e)if("object"==typeof(r=e)&&(u=n.call(r),!0===t[u]||"number"==typeof r.length&&r.length>=0&&(0===r.length||"object"==typeof r[0]&&r[0].nodeType>0)))for(var c=0;c<e.length;c++)this.add(e[c]);else(e instanceof Set||"Set"===e.baseType)&&e.forEach(function(t){this.add(t)},this)}var c=0,l="obj_",f="__objectPolyFillID",h={string:!0,boolean:!0,number:!0,undefined:!0};function d(t,e){var r,o=typeof t;if(h[o])return o.substr(0,3)+"_"+t;if(null===t)return"nul_null";if("object"===o||"function"===o)return t[f]?t[f]:e?(r=l+c++,"[object Object]"===n.call(t)&&i?Object.defineProperty(t,f,{enumerable:!1,configurable:!1,writable:!1,value:r}):t[f]=r,r):null;throw new Error("Unsupported type for Set.add()")}function b(t,e,n){var i=0,o=t.length;this.next=function(){for(var a,u,s={};;){if(i<o){if(s.done=!1,u=t[i++],void 0===(a=e[u])&&!r(e,u))continue;"keys"===n?s.value=a:"entries"===n&&(s.value=[a,a])}else t=null,e=null,s.done=!0;return s}}}function p(t){var e=[];for(var n in t)r(t,n)&&e.push(n);return e}return s.prototype={add:function(t){var e=d(t,!0);return r(this._data,e)||(this._data[e]=t,u(this,this.size+1)),this},clear:function(){this._data={},u(this,0)},delete:function(t){var e=d(t,!1);return!(null===e||!r(this._data,e))&&(delete this._data[e],u(this,this.size-1),!0)},remove:function(t){return this.delete(t)},forEach:function(t){if("function"==typeof t)for(var e,n,r=arguments[1],i=this.keys();(e=i.next())&&!e.done;)n=e.value,t.call(r,n,n,this)},has:function(t){var n=d(t,!1);return null!==n&&e.call(this._data,n)},values:function(){return this.keys()},keys:function(){return new b(p(this._data),this._data,"keys")},entries:function(){return new b(p(this._data),this._data,"entries")}},s.prototype.constructor=s,s}(); 
if(window.navigator.userAgent.indexOf('+UE4+')==-1){// UE4内核的不加载aegis，看看是否有异常
    /**
 *  ===========================================================================
 * @tencent/aegis-web-sdk@1.43.19 (c) 2024 TencentCloud Real User Monitoring.
 * Author pumpkincai.
 * Last Release Time Tue Dec 31 2024 17:21:04 GMT+0800 (GMT+08:00).
 * Released under the MIT License.
 * Thanks for supporting RUM & Aegis!
 * ===========================================================================
 **/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Aegis=t()}(this,function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t,n){return e(n={path:t,exports:{},require:function(e,t){throw t!==undefined&&null!==t||n.path,new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}},n.exports),n.exports}var M,_,B,d,F,x=(B=function(e){return e&&e.Math===Math&&e})("object"==typeof globalThis&&globalThis)||B("object"==typeof window&&window)||B("object"==typeof self&&self)||B("object"==typeof e&&e)||B("object"==typeof e&&e)||function(){return this}()||Function("return this")(),A=!(d=function(e){try{return!!e()}catch(t){return!0}})(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}),q=!d(function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}),D=Function.prototype.call,P=q?D.bind(D):function(){return D.apply(D,arguments)},e={}.propertyIsEnumerable,H={f:(F=Object.getOwnPropertyDescriptor)&&!e.call({1:2},1)?function(e){e=F(this,e);return!!e&&e.enumerable}:e},W=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},e=Function.prototype,V=e.call,e=q&&e.bind.bind(V,V),h=q?e:function(e){return function(){return V.apply(e,arguments)}},G=h({}.toString),$=h("".slice),z=function(e){return $(G(e),8,-1)},X=Object,Y=h("".split),J=d(function(){return!X("z").propertyIsEnumerable(0)})?function(e){return"String"===z(e)?Y(e,""):X(e)}:X,K=function(e){return null===e||e===undefined},Q=TypeError,Z=function(e){if(K(e))throw new Q("Can't call method on "+e);return e},ee=function(e){return J(Z(e))},te="object"==typeof document&&document.all,R=void 0===te&&te!==undefined?function(e){return"function"==typeof e||e===te}:function(e){return"function"==typeof e},I=function(e){return"object"==typeof e?null!==e:R(e)},ne=function(e,t){return arguments.length<2?(n=x[e],R(n)?n:undefined):x[e]&&x[e][t];var n},re=h({}.isPrototypeOf),e=x.navigator,e=e&&e.userAgent,oe=e?String(e):"",e=x.process,n=x.Deno,e=e&&e.versions||n&&n.version,n=e&&e.v8,ie=_=!(_=n?0<(M=n.split("."))[0]&&M[0]<4?1:+(M[0]+M[1]):_)&&oe&&(!(M=oe.match(/Edge\/(\d+)/))||74<=M[1])&&(M=oe.match(/Chrome\/(\d+)/))?+M[1]:_,ae=x.String,ue=!!Object.getOwnPropertySymbols&&!d(function(){var e=Symbol("symbol detection");return!ae(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&ie&&ie<41}),e=ue&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,se=Object,ce=e?function(e){return"symbol"==typeof e}:function(e){var t=ne("Symbol");return R(t)&&re(t.prototype,se(e))},fe=String,le=function(e){try{return fe(e)}catch(t){return"Object"}},de=TypeError,he=function(e){if(R(e))return e;throw new de(le(e)+" is not a function")},pe=function(e,t){t=e[t];return K(t)?undefined:he(t)},ge=TypeError,ye=Object.defineProperty,ve=function(e,t){try{ye(x,e,{value:t,configurable:!0,writable:!0})}catch(M){x[e]=t}return t},me=t(function(e){e=e.exports=x["__core-js_shared__"]||ve("__core-js_shared__",{});(e.versions||(e.versions=[])).push({version:"3.39.0",mode:"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})}),n=function(e,t){return me[e]||(me[e]=t||{})},be=Object,we=function(e){return be(Z(e))},Ee=h({}.hasOwnProperty),L=Object.hasOwn||function(e,t){return Ee(we(e),t)},Re=0,Se=Math.random(),Oe=h(1..toString),Te=function(e){return"Symbol("+(e===undefined?"":e)+")_"+Oe(++Re+Se,36)},xe=x.Symbol,Ae=n("wks"),Pe=e?xe["for"]||xe:xe&&xe.withoutSetter||Te,s=function(e){return L(Ae,e)||(Ae[e]=ue&&L(xe,e)?xe[e]:Pe("Symbol."+e)),Ae[e]},Ie=TypeError,Le=s("toPrimitive"),ke=function(e,t){if(!I(e)||ce(e))return e;var n,r,o=pe(e,Le);if(o){if(t===undefined&&(t="default"),o=P(o,e,t),!I(o)||ce(o))return o;throw new Ie("Can't convert object to primitive value")}t===undefined&&(t="number");o=e,e=t;if("string"===e&&R(n=o.toString)&&!I(r=P(n,o)))return r;if(R(n=o.valueOf)&&!I(r=P(n,o)))return r;if("string"!==e&&R(n=o.toString)&&!I(r=P(n,o)))return r;throw new ge("Can't convert object to primitive value")},Ce=function(e){e=ke(e,"string");return ce(e)?e:e+""},je=x.document,Ue=I(je)&&I(je.createElement),Ne=function(e){return Ue?je.createElement(e):{}},Me=!A&&!d(function(){return 7!==Object.defineProperty(Ne("div"),"a",{get:function(){return 7}}).a}),_e=Object.getOwnPropertyDescriptor,Be={f:A?_e:function(e,t){if(e=ee(e),t=Ce(t),Me)try{return _e(e,t)}catch(M){}if(L(e,t))return W(!P(H.f,e,t),e[t])}},e=A&&d(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype}),Fe=String,qe=TypeError,S=function(e){if(I(e))return e;throw new qe(Fe(e)+" is not an object")},De=TypeError,He=Object.defineProperty,We=Object.getOwnPropertyDescriptor,Ve={f:A?e?function(e,t,n){var r;return S(e),t=Ce(t),S(n),"function"==typeof e&&"prototype"===t&&"value"in n&&"writable"in n&&!n.writable&&(r=We(e,t))&&r.writable&&(e[t]=n.value,n={configurable:("configurable"in n?n:r).configurable,enumerable:("enumerable"in n?n:r).enumerable,writable:!1}),He(e,t,n)}:He:function(e,t,n){if(S(e),t=Ce(t),S(n),Me)try{return He(e,t,n)}catch(_){}if("get"in n||"set"in n)throw new De("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},Ge=A?function(e,t,n){return Ve.f(e,t,W(1,n))}:function(e,t,n){return e[t]=n,e},$e=Function.prototype,r=A&&Object.getOwnPropertyDescriptor,ze=L($e,"name"),Xe={EXISTS:ze,PROPER:ze&&"something"===function(){}.name,CONFIGURABLE:ze&&(!A||r($e,"name").configurable)},Ye=h(Function.toString);R(me.inspectSource)||(me.inspectSource=function(e){return Ye(e)});var Je,Ke,Qe,Ze,et=me.inspectSource,ze=x.WeakMap,$e=R(ze)&&/native code/.test(String(ze)),tt=n("keys"),r=function(e){return tt[e]||(tt[e]=Te(e))},nt={},rt=x.TypeError,ze=x.WeakMap,ot=$e||me.state?((Qe=me.state||(me.state=new ze)).get=Qe.get,Qe.has=Qe.has,Qe.set=Qe.set,Je=function(e,t){if(Qe.has(e))throw new rt("Object already initialized");return t.facade=e,Qe.set(e,t),t},Ke=function(e){return Qe.get(e)||{}},function(e){return Qe.has(e)}):(Ze=r("state"),nt[Ze]=!0,Je=function(e,t){if(L(e,Ze))throw new rt("Object already initialized");return t.facade=e,Ge(e,Ze,t),t},Ke=function(e){return L(e,Ze)?e[Ze]:{}},function(e){return L(e,Ze)}),k={set:Je,get:Ke,has:ot,enforce:function(e){return ot(e)?Ke(e):Je(e,{})},getterFor:function(t){return function(e){if(I(e)&&(e=Ke(e)).type===t)return e;throw new rt("Incompatible receiver, "+t+" required")}}},it=t(function(e){var r=Xe.CONFIGURABLE,o=k.enforce,t=k.get,i=String,a=Object.defineProperty,u=h("".slice),s=h("".replace),c=h([].join),f=A&&!d(function(){return 8!==a(function(){},"length",{value:8}).length}),l=String(String).split("String"),e=e.exports=function(e,t,n){"Symbol("===u(i(t),0,7)&&(t="["+s(i(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!L(e,"name")||r&&e.name!==t)&&(A?a(e,"name",{value:t,configurable:!0}):e.name=t),f&&n&&L(n,"arity")&&e.length!==n.arity&&a(e,"length",{value:n.arity});try{n&&L(n,"constructor")&&n.constructor?A&&a(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=undefined)}catch(W){}n=o(e);return L(n,"source")||(n.source=c(l,"string"==typeof t?t:"")),e};Function.prototype.toString=e(function(){return R(this)&&t(this).source||et(this)},"toString")}),g=function(e,t,n,r){var o=(r=r||{}).enumerable,i=r.name!==undefined?r.name:t;if(R(n)&&it(n,i,r),r.global)o?e[t]=n:ve(t,n);else{try{r.unsafe?e[t]&&(o=!0):delete e[t]}catch(d){}o?e[t]=n:Ve.f(e,t,{value:n,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return e},at=Math.ceil,ut=Math.floor,st=Math.trunc||function(e){e=+e;return(0<e?ut:at)(e)},ct=function(e){e=+e;return e!=e||0==e?0:st(e)},ft=Math.max,lt=Math.min,dt=function(e,t){e=ct(e);return e<0?ft(e+t,0):lt(e,t)},ht=Math.min,pt=function(e){e=ct(e);return 0<e?ht(e,9007199254740991):0},gt=function(e){return pt(e.length)},$e=function(u){return function(e,t,n){var r=ee(e),o=gt(r);if(0!==o){var i,a=dt(n,o);if(u&&t!=t){for(;a<o;)if((i=r[a++])!=i)return!0}else for(;a<o;a++)if((u||a in r)&&r[a]===t)return u||a||0}return!u&&-1}},ze={includes:$e(!0),indexOf:$e(!1)},yt=ze.indexOf,vt=h([].push),mt=function(e,t){var n,r=ee(e),o=0,i=[];for(n in r)!L(nt,n)&&L(r,n)&&vt(i,n);for(;t.length>o;)!L(r,n=t[o++])||~yt(i,n)||vt(i,n);return i},bt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],wt=bt.concat("length","prototype"),Et={f:Object.getOwnPropertyNames||function(e){return mt(e,wt)}},Rt={f:Object.getOwnPropertySymbols},St=h([].concat),Ot=ne("Reflect","ownKeys")||function(e){var t=Et.f(S(e)),n=Rt.f;return n?St(t,n(e)):t},Tt=function(e,t,n){for(var r=Ot(t),o=Ve.f,i=Be.f,a=0;a<r.length;a++){var u=r[a];L(e,u)||n&&L(n,u)||o(e,u,i(t,u))}},xt=/#|\.prototype\./,$e=function(e,t){e=Pt[At(e)];return e===Lt||e!==It&&(R(t)?d(t):!!t)},At=$e.normalize=function(e){return String(e).replace(xt,".").toLowerCase()},Pt=$e.data={},It=$e.NATIVE="N",Lt=$e.POLYFILL="P",kt=$e,Ct=Be.f,C=function(e,t){var n,r,o,i,a=e.target,u=e.global,s=e.stat;if(n=u?x:s?x[a]||ve(a,{}):x[a]&&x[a].prototype)for(r in t){if(o=t[r],i=e.dontCallGetSet?(i=Ct(n,r))&&i.value:n[r],!kt(u?r:a+(s?".":"#")+r,e.forced)&&i!==undefined){if(typeof o==typeof i)continue;Tt(o,i)}(e.sham||i&&i.sham)&&Ge(o,"sham",!0),g(n,r,o,e)}},$e=function(e){if("Function"===z(e))return h(e)},jt=$e($e.bind),Ut=function(e,t){return he(e),t===undefined?e:q?jt(e,t):function(){return e.apply(t,arguments)}},Nt=Array.isArray||function(e){return"Array"===z(e)},o={},o=(o[s("toStringTag")]="z","[object z]"===String(o)),Mt=s("toStringTag"),_t=Object,Bt="Arguments"===z(function(){return arguments}()),Ft=o?z:function(e){var n,t;return e===undefined?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(e=_t(e),Mt))?n:Bt?z(e):"Object"===(t=z(e))&&R(e.callee)?"Arguments":t},qt=function(){},Dt=ne("Reflect","construct"),Ht=/^\s*(?:class|function)\b/,Wt=h(Ht.exec),Vt=!Ht.test(qt),Gt=function(e){if(!R(e))return!1;try{return Dt(qt,[],e),!0}catch(t){return!1}},$t=function(e){if(!R(e))return!1;switch(Ft(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Vt||!!Wt(Ht,et(e))}catch(t){return!0}};$t.sham=!0;var zt,Xt=!Dt||d(function(){var e;return Gt(Gt.call)||!Gt(Object)||!Gt(function(){e=!0})||e})?$t:Gt,Yt=s("species"),Jt=Array,Kt=function(e,t){return new((n=Nt(e=e)&&(n=e.constructor,Xt(n)&&(n===Jt||Nt(n.prototype))||I(n)&&null===(n=n[Yt]))?undefined:n)===undefined?Jt:n)(0===t?0:t);var n},Qt=h([].push),$t=function(d){var h=1===d,p=2===d,g=3===d,y=4===d,v=6===d,m=7===d,b=5===d||v;return function(e,t,n,r){for(var o,i,a=we(e),u=J(a),s=gt(u),c=Ut(t,n),f=0,t=r||Kt,l=h?t(e,s):p||m?t(e,0):undefined;f<s;f++)if((b||f in u)&&(i=c(o=u[f],f,a),d))if(h)l[f]=i;else if(i)switch(d){case 3:return!0;case 5:return o;case 6:return f;case 2:Qt(l,o)}else switch(d){case 4:return!1;case 7:Qt(l,o)}return v?-1:g||y?y:l}},Zt={forEach:$t(0),map:$t(1),filter:$t(2),some:$t(3),every:$t(4),find:$t(5),findIndex:$t(6),filterReject:$t(7)},en=Object.keys||function(e){return mt(e,bt)},tn={f:A&&!e?Object.defineProperties:function(e,t){S(e);for(var n,r=ee(t),o=en(t),i=o.length,a=0;a<i;)Ve.f(e,n=o[a++],r[n]);return e}},nn=ne("document","documentElement"),rn=r("IE_PROTO"),on=function(){},an=function(e){return"<script>"+e+"<\/script>"},un=function(e){e.write(an("")),e.close();var t=e.parentWindow.Object;return e=null,t},sn=function(){try{zt=new ActiveXObject("htmlfile")}catch(_){}var e;sn="undefined"==typeof document||document.domain&&zt?un(zt):((e=Ne("iframe")).style.display="none",nn.appendChild(e),e.src=String("javascript:"),(e=e.contentWindow.document).open(),e.write(an("document.F=Object")),e.close(),e.F);for(var t=bt.length;t--;)delete sn.prototype[bt[t]];return sn()},cn=(nt[rn]=!0,Object.create||function(e,t){var n;return null!==e?(on.prototype=S(e),n=new on,on.prototype=null,n[rn]=e):n=sn(),t===undefined?n:tn.f(n,t)}),$t=Ve.f,fn=s("unscopables"),ln=Array.prototype,e=(ln[fn]===undefined&&$t(ln,fn,{configurable:!0,value:cn(null)}),function(e){ln[fn][e]=!0}),dn=Zt.find,hn=!0,$t=("find"in[]&&Array(1).find(function(){hn=!1}),C({target:"Array",proto:!0,forced:hn},{find:function(e){return dn(this,e,1<arguments.length?arguments[1]:undefined)}}),e("find"),function(e,t){var n=[][e];return!!n&&d(function(){n.call(null,t||function(){return 1},1)})}),pn=Zt.forEach,gn=$t("forEach")?[].forEach:function(e){return pn(this,e,1<arguments.length?arguments[1]:undefined)};C({target:"Array",proto:!0,forced:[].forEach!==gn},{forEach:gn});var yn,vn={},mn=!d(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),bn=r("IE_PROTO"),wn=Object,En=wn.prototype,Rn=mn?wn.getPrototypeOf:function(e){var t,e=we(e);return L(e,bn)?e[bn]:(t=e.constructor,R(t)&&e instanceof t?t.prototype:e instanceof wn?En:null)},Sn=s("iterator"),r=!1,i=([].keys&&("next"in(i=[].keys())?(i=Rn(Rn(i)))!==Object.prototype&&(yn=i):r=!0),I(yn)&&!d(function(){var e={};return yn[Sn].call(e)!==e})||(yn={}),R(yn[Sn])||g(yn,Sn,function(){return this}),{IteratorPrototype:yn,BUGGY_SAFARI_ITERATORS:r}),On=Ve.f,Tn=s("toStringTag"),xn=function(e,t,n){(e=e&&!n?e.prototype:e)&&!L(e,Tn)&&On(e,Tn,{configurable:!0,value:t})},An=i.IteratorPrototype,Pn=function(){return this},In=function(e,t,n,r){t+=" Iterator";return e.prototype=cn(An,{next:W(+!r,n)}),xn(e,t,!1),vn[t]=Pn,e},Ln=String,kn=TypeError,Cn=Object.setPrototypeOf||("__proto__"in{}?function(){var n,r=!1,e={};try{(n=function(e){try{return h(he(Object.getOwnPropertyDescriptor(e,"__proto__").set))}catch(_){}}(Object.prototype))(e,[]),r=e instanceof Array}catch(_){}return function(e,t){return Z(e),function(e){if(I(t=e)||null===t)return;var t;throw new kn("Can't set "+Ln(e)+" as a prototype")}(t),I(e)&&(r?n(e,t):e.__proto__=t),e}}():undefined),jn=Xe.PROPER,Un=Xe.CONFIGURABLE,Nn=i.IteratorPrototype,Mn=i.BUGGY_SAFARI_ITERATORS,_n=s("iterator"),Bn=function(){return this},r=function(e,t,n,r,o,i,a){In(n,t,r);var u,s,r=function(e){if(e===o&&h)return h;if(!Mn&&e&&e in l)return l[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},c=t+" Iterator",f=!1,l=e.prototype,d=l[_n]||l["@@iterator"]||o&&l[o],h=!Mn&&d||r(o),p="Array"===t&&l.entries||d;if(p&&(e=Rn(p.call(new e)))!==Object.prototype&&e.next&&(Rn(e)!==Nn&&(Cn?Cn(e,Nn):R(e[_n])||g(e,_n,Bn)),xn(e,c,!0)),jn&&"values"===o&&d&&"values"!==d.name&&(Un?Ge(l,"name","values"):(f=!0,h=function(){return P(d,this)})),o)if(u={values:r("values"),keys:i?h:r("keys"),entries:r("entries")},a)for(s in u)!Mn&&!f&&s in l||g(l,s,u[s]);else C({target:t,proto:!0,forced:Mn||f},u);return l[_n]!==h&&g(l,_n,h,{name:o}),vn[t]=h,u},Fn=function(e,t){return{value:e,done:t}},a=Ve.f,qn=k.set,Dn=k.getterFor("Array Iterator"),Hn=r(Array,"Array",function(e,t){qn(this,{type:"Array Iterator",target:ee(e),index:0,kind:t})},function(){var e=Dn(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,Fn(undefined,!0);switch(e.kind){case"keys":return Fn(n,!1);case"values":return Fn(t[n],!1)}return Fn([n,t[n]],!1)},"values"),Wn=vn.Arguments=vn.Array;if(e("keys"),e("values"),e("entries"),A&&"values"!==Wn.name)try{a(Wn,"name",{value:"values"})}catch(N){}var Vn=Date,Gn=h(Vn.prototype.getTime),c=(C({target:"Date",stat:!0},{now:function(){return Gn(new Vn)}}),function(e,t,n){return n.get&&it(n.get,t,{getter:!0}),n.set&&it(n.set,t,{setter:!0}),Ve.f(e,t,n)}),Wn=Xe.EXISTS,a=Function.prototype,$n=h(a.toString),zn=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,Xn=h(zn.exec),Wn=(A&&!Wn&&c(a,"name",{configurable:!0,get:function(){try{return Xn(zn,$n(this))[1]}catch(N){return""}}}),Ve.f),a=(C({target:"Object",stat:!0,forced:Object.defineProperty!==Wn,sham:!A},{defineProperty:Wn}),o?{}.toString:function(){return"[object "+Ft(this)+"]"}),Yn=(o||g(Object.prototype,"toString",a,{unsafe:!0}),String),O=function(e){if("Symbol"===Ft(e))throw new TypeError("Cannot convert a Symbol value to a string");return Yn(e)},Jn=h("".charAt),Kn=h("".charCodeAt),Qn=h("".slice),Wn=function(o){return function(e,t){var n,e=O(Z(e)),t=ct(t),r=e.length;return t<0||r<=t?o?"":undefined:(n=Kn(e,t))<55296||56319<n||t+1===r||(r=Kn(e,t+1))<56320||57343<r?o?Jn(e,t):n:o?Qn(e,t,t+2):r-56320+(n-55296<<10)+65536}},o={codeAt:Wn(!1),charAt:Wn(!0)},Zn=o.charAt,er=k.set,tr=k.getterFor("String Iterator"),m=(r(String,"String",function(e){er(this,{type:"String Iterator",string:O(e),index:0})},function(){var e=tr(this),t=e.string,n=e.index;return n>=t.length?Fn(undefined,!0):(t=Zn(t,n),e.index+=t.length,Fn(t,!1))}),h([].slice)),nr=Et.f,rr="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],or={f:function(e){if(!rr||"Window"!==z(e))return nr(ee(e));try{return nr(e)}catch(N){return m(rr)}}},ir=d(function(){var e;"function"==typeof ArrayBuffer&&(e=new ArrayBuffer(8),Object.isExtensible(e))&&Object.defineProperty(e,"a",{value:8})}),ar=Object.isExtensible,ur=d(function(){ar(1)})||ir?function(e){return!!I(e)&&(!ir||"ArrayBuffer"!==z(e))&&(!ar||ar(e))}:ar,sr=!d(function(){return Object.isExtensible(Object.preventExtensions({}))}),a=t(function(e){var t=Ve.f,n=!1,a=Te("meta"),r=0,o=function(e){t(e,a,{value:{objectID:"O"+r++,weakData:{}}})},u=e.exports={enable:function(){u.enable=function(){},n=!0;var o=Et.f,i=h([].splice),e={};e[a]=1,o(e).length&&(Et.f=function(e){for(var t=o(e),n=0,r=t.length;n<r;n++)if(t[n]===a){i(t,n,1);break}return t},C({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:or.f}))},fastKey:function(e,t){if(!I(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!L(e,a)){if(!ur(e))return"F";if(!t)return"E";o(e)}return e[a].objectID},getWeakData:function(e,t){if(!L(e,a)){if(!ur(e))return!0;if(!t)return!1;o(e)}return e[a].weakData},onFreeze:function(e){return sr&&n&&ur(e)&&!L(e,a)&&o(e),e}};nt[a]=!0}),cr=s("iterator"),fr=Array.prototype,lr=function(e){return e!==undefined&&(vn.Array===e||fr[cr]===e)},dr=s("iterator"),hr=function(e){if(!K(e))return pe(e,dr)||pe(e,"@@iterator")||vn[Ft(e)]},pr=TypeError,gr=function(e,t){t=arguments.length<2?hr(e):t;if(he(t))return S(P(t,e));throw new pr(le(e)+" is not iterable")},yr=function(e,t,n){var r,o;S(e);try{if(!(r=pe(e,"return"))){if("throw"===t)throw n;return n}r=P(r,e)}catch(N){o=!0,r=N}if("throw"===t)throw n;if(o)throw r;return S(r),n},vr=TypeError,mr=function(e,t){this.stopped=e,this.result=t},br=mr.prototype,wr=function(e,t,n){var r,o,i,a,u,s,c=n&&n.that,f=!(!n||!n.AS_ENTRIES),l=!(!n||!n.IS_RECORD),d=!(!n||!n.IS_ITERATOR),h=!(!n||!n.INTERRUPTED),p=Ut(t,c),g=function(e){return r&&yr(r,"normal",e),new mr(!0,e)},y=function(e){return f?(S(e),h?p(e[0],e[1],g):p(e[0],e[1])):h?p(e,g):p(e)};if(l)r=e.iterator;else if(d)r=e;else{if(!(n=hr(e)))throw new vr(le(e)+" is not iterable");if(lr(n)){for(o=0,i=gt(e);o<i;o++)if((a=y(e[o]))&&re(br,a))return a;return new mr(!1)}r=gr(e,n)}for(u=(l?e:r).next;!(s=P(u,r)).done;){try{a=y(s.value)}catch(N){yr(r,"throw",N)}if("object"==typeof a&&a&&re(br,a))return a}return new mr(!1)},Er=TypeError,Rr=function(e,t){if(re(t,e))return e;throw new Er("Incorrect invocation")},Sr=s("iterator"),Or=!1;try{var Tr=0,xr={next:function(){return{done:!!Tr++}},"return":function(){Or=!0}};xr[Sr]=function(){return this},Array.from(xr,function(){throw 2})}catch(N){}var Ar,Pr,Ir,Lr,kr,Cr,jr,Ur,Wn=function(e,t){try{if(!t&&!Or)return!1}catch(N){return!1}var n=!1;try{var r={};r[Sr]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(N){}return n},Nr=function(e,t,n){return Cn&&R(t=t.constructor)&&t!==n&&I(t=t.prototype)&&t!==n.prototype&&Cn(e,t),e},Mr=function(e,t,n){for(var r in t)g(e,r,t[r],n);return e},_r=a.getWeakData,Br=k.set,Fr=k.getterFor,qr=Zt.find,Dr=Zt.findIndex,Hr=h([].splice),Wr=0,Vr=function(e){return e.frozen||(e.frozen=new Gr)},Gr=function(){this.entries=[]},$r=function(e,t){return qr(e.entries,function(e){return e[0]===t})},zr=(Gr.prototype={get:function(e){e=$r(this,e);if(e)return e[1]},has:function(e){return!!$r(this,e)},set:function(e,t){var n=$r(this,e);n?n[1]=t:this.entries.push([e,t])},"delete":function(t){var e=Dr(this.entries,function(e){return e[0]===t});return~e&&Hr(this.entries,e,1),!!~e}},r=function(e){return function(){return e(this,arguments.length?arguments[0]:undefined)}},xr={getConstructor:function(e,n,r,o){var e=e(function(e,t){Rr(e,i),Br(e,{type:n,id:Wr++,frozen:null}),K(t)||wr(t,e[o],{that:e,AS_ENTRIES:r})}),i=e.prototype,a=Fr(n),u=function(e,t,n){var r=a(e),o=_r(S(t),!0);return!0===o?Vr(r).set(t,n):o[r.id]=n,e};return Mr(i,{"delete":function(e){var t,n=a(this);return!!I(e)&&(!0===(t=_r(e))?Vr(n)["delete"](e):t&&L(t,n.id)&&delete t[n.id])},has:function(e){var t,n=a(this);return!!I(e)&&(!0===(t=_r(e))?Vr(n).has(e):t&&L(t,n.id))}}),Mr(i,r?{get:function(e){var t,n=a(this);if(I(e))return!0===(t=_r(e))?Vr(n).get(e):t?t[n.id]:void 0},set:function(e,t){return u(this,e,t)}}:{add:function(e){return u(this,e,!0)}}),e}},Pr=-1!==(go="WeakSet").indexOf("Map"),Ir=-1!==go.indexOf("Weak"),Lr=Pr?"set":"add",kr=x[go],Cr=kr&&kr.prototype,no={},u=function(e){var n=h(Cr[e]);g(Cr,e,"add"===e?function(e){return n(this,0===e?0:e),this}:"delete"===e?function(e){return!(Ir&&!I(e))&&n(this,0===e?0:e)}:"get"===e?function(e){return Ir&&!I(e)?undefined:n(this,0===e?0:e)}:"has"===e?function(e){return!(Ir&&!I(e))&&n(this,0===e?0:e)}:function(e,t){return n(this,0===e?0:e,t),this})},kt(go,!R(jr=kr)||!(Ir||Cr.forEach&&!d(function(){(new kr).entries().next()})))?(jr=xr.getConstructor(r,go,Pr,Lr),a.enable()):kt(go,!0)&&(a=(Ar=new jr)[Lr](Ir?{}:-0,1)!==Ar,Qr=d(function(){Ar.has(1)}),Yr=Wn(function(e){new kr(e)}),Zr=!Ir&&d(function(){for(var e=new kr,t=5;t--;)e[Lr](t,t);return!e.has(-0)}),Yr||(((jr=r(function(e,t){Rr(e,Cr);e=Nr(new kr,e,jr);return K(t)||wr(t,e[Lr],{that:e,AS_ENTRIES:Pr}),e})).prototype=Cr).constructor=jr),(Qr||Zr)&&(u("delete"),u("has"),Pr)&&u("get"),(Zr||a)&&u(Lr),Ir)&&Cr.clear&&delete Cr.clear,C({global:!0,constructor:!0,forced:(no[go]=jr)!==kr},no),xn(jr,go),Ir||xr.setStrong(jr,go,Pr),function(e,t,n){A?Ve.f(e,t,W(0,n)):e[t]=n}),Xr=i.IteratorPrototype,Yr=s("toStringTag"),Jr=TypeError,Kr=x.Iterator,r=!R(Kr)||Kr.prototype!==Xr||!d(function(){Kr({})}),Qr=function(){if(Rr(this,Xr),Rn(this)===Xr)throw new Jr("Abstract class Iterator not directly constructable")},Zr=function(t,e){A?c(Xr,t,{configurable:!0,get:function(){return e},set:function(e){if(S(this),this===Xr)throw new Jr("You can't redefine this property");L(this,t)?this[t]=e:zr(this,t,e)}}):Xr[t]=e},eo=(L(Xr,Yr)||Zr(Yr,"Iterator"),!r&&L(Xr,"constructor")&&Xr.constructor!==Object||Zr("constructor",Qr),Qr.prototype=Xr,C({global:!0,constructor:!0,forced:r},{Iterator:Qr}),function(e){return{iterator:e,next:e.next,done:!1}}),to=(C({target:"Iterator",proto:!0,real:!0},{forEach:function(t){S(this),he(t);var e=eo(this),n=0;wr(e,function(e){t(e,n++)},{IS_RECORD:!0})}}),{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}),a=Ne("span").classList,u=a&&a.constructor&&a.constructor.prototype,no=u===Object.prototype?undefined:u,ro=function(e){if(e&&e.forEach!==gn)try{Ge(e,"forEach",gn)}catch(N){e.forEach=gn}};for(Ur in to)to[Ur]&&ro(x[Ur]&&x[Ur].prototype);ro(no);var oo,io,ao,uo=s("iterator"),so=Hn.values,co=function(e,t){if(e){if(e[uo]!==so)try{Ge(e,uo,so)}catch(N){e[uo]=so}if(xn(e,t,!0),to[t])for(var n in Hn)if(e[n]!==Hn[n])try{Ge(e,n,Hn[n])}catch(N){e[n]=Hn[n]}}};for(oo in to)co(x[oo]&&x[oo].prototype,oo);function fo(e){this.name="__st"+(1e9*Math.random()>>>0)+io+"__",null!=e&&e.forEach(this.add,this),io+=1}co(no,"DOMTokenList"),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{configurable:!0,writable:!0,value:function(e){if(null===this)throw new TypeError('"this" is null or not defined');var t=Object(this),n=t.length>>>0;if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var r=arguments[1],o=0;o<n;){var i=t[o];if(e.call(r,i,o,t))return i;o+=1}return undefined}}),window.WeakSet||(io=Date.now()%1e9,fo.prototype.add=function(e){var t=this.name;return e[t]||Object.defineProperty(e,t,{value:!0,writable:!0}),this},fo.prototype["delete"]=function(e){return!!e[this.name]&&(e[this.name]=undefined,!0)},fo.prototype.has=function(e){return!!e[this.name]},ao=fo,Object.defineProperty(window,"WeakSet",{value:function(e){return new ao(e)}}));var lo=Object.assign,ho=Object.defineProperty,po=h([].concat),go=!lo||d(function(){var e,t,n;return!(!A||1===lo({b:1},lo(ho({},"a",{enumerable:!0,get:function(){ho(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)||(t={},(e={})[n=Symbol("assign detection")]=7,"abcdefghijklmnopqrst".split("").forEach(function(e){t[e]=e}),7!==lo({},e)[n])||"abcdefghijklmnopqrst"!==en(lo({},t)).join("")})?function(e,t){for(var n=we(e),r=arguments.length,o=1,i=Rt.f,a=H.f;o<r;)for(var u,s=J(arguments[o++]),c=i?po(en(s),i(s)):en(s),f=c.length,l=0;l<f;)u=c[l++],A&&!P(a,s,u)||(n[u]=s[u]);return n}:lo,yo=(C({target:"Object",stat:!0,arity:2,forced:Object.assign!==go},{assign:go}),Be.f),r=!A||d(function(){yo(1)}),a=(C({target:"Object",stat:!0,forced:r,sham:!A},{getOwnPropertyDescriptor:function(e,t){return yo(ee(e),t)}}),d(function(){en(1)}));function vo(e){return(vo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}C({target:"Object",stat:!0,forced:a},{keys:function(e){return en(we(e))}}),Object.assign||Object.defineProperty(Object,"assign",{enumerable:!1,configurable:!0,writable:!0,value:function(e){if(e===undefined||null===e)throw new TypeError("Cannot convert first argument to object");for(var t=Object(e),n=1;n<arguments.length;n++)if((r=arguments[n])!==undefined&&null!==r)for(var r=Object(r),o=Object.keys(Object(r)),i=0,a=o.length;i<a;i++){var u=o[i],s=Object.getOwnPropertyDescriptor(r,u);null!=s&&s.enumerable&&(t[u]=r[u])}return t}});var mo=s("species"),u=function(t){return 51<=ie||!d(function(){var e=[];return(e.constructor={})[mo]=function(){return{foo:1}},1!==e[t](Boolean).foo})},bo=Zt.filter,no=u("filter"),wo=(C({target:"Array",proto:!0,forced:!no},{filter:function(e){return bo(this,e,1<arguments.length?arguments[1]:undefined)}}),ze.indexOf),Eo=$e([].indexOf),Ro=!!Eo&&1/Eo([1],1,-0)<0,r=Ro||!$t("indexOf"),a=(C({target:"Array",proto:!0,forced:r},{indexOf:function(e){var t=1<arguments.length?arguments[1]:undefined;return Ro?Eo(this,e,t)||0:wo(this,e,t)}}),u("slice")),So=s("species"),Oo=Array,To=Math.max,xo=(C({target:"Array",proto:!0,forced:!a},{slice:function(e,t){var n,r,o,i=ee(this),a=gt(i),u=dt(e,a),s=dt(t===undefined?a:t,a);if(Nt(i)&&(n=i.constructor,(n=Xt(n)&&(n===Oo||Nt(n.prototype))||I(n)&&null===(n=n[So])?undefined:n)===Oo||n===undefined))return m(i,u,s);for(r=new(n===undefined?Oo:n)(To(s-u,0)),o=0;u<s;u++,o++)u in i&&zr(r,o,i[u]);return r.length=o,r}}),TypeError),Ao=Object.getOwnPropertyDescriptor,Po=A&&!function(){if(this!==undefined)return 1;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(N){return N instanceof TypeError}}()?function(e,t){if(Nt(e)&&!Ao(e,"length").writable)throw new xo("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t},Io=TypeError,Lo=function(e){if(9007199254740991<e)throw Io("Maximum allowed index exceeded");return e},ko=TypeError,Co=function(e,t){if(!delete e[t])throw new ko("Cannot delete property "+le(t)+" of "+le(e))},no=u("splice"),jo=Math.max,Uo=Math.min,r=(C({target:"Array",proto:!0,forced:!no},{splice:function(e,t){var n,r,o,i,a,u,s=we(this),c=gt(s),f=dt(e,c),e=arguments.length;for(0===e?n=r=0:r=1===e?(n=0,c-f):(n=e-2,Uo(jo(ct(t),0),c-f)),Lo(c+n-r),o=Kt(s,r),i=0;i<r;i++)(a=f+i)in s&&zr(o,i,s[a]);if(n<(o.length=r)){for(i=f;i<c-r;i++)u=i+n,(a=i+r)in s?s[u]=s[a]:Co(s,u);for(i=c;c-r+n<i;i--)Co(s,i-1)}else if(r<n)for(i=c-r;f<i;i--)u=i+n-1,(a=i+r-1)in s?s[u]=s[a]:Co(s,u);for(i=0;i<n;i++)s[i+f]=arguments[i+2];return Po(s,c-r+n),o}}),d(function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})})),a=(C({target:"Date",proto:!0,arity:1,forced:r},{toJSON:function(e){var t=we(this),n=ke(t,"number");return"number"!=typeof n||isFinite(n)?t.toISOString():null}}),Function.prototype),No=a.apply,Mo=a.call,_o="object"==typeof Reflect&&Reflect.apply||(q?Mo.bind(No):function(){return Mo.apply(No,arguments)}),Bo=h([].push),Fo=String,qo=ne("JSON","stringify"),Do=h(/./.exec),Ho=h("".charAt),Wo=h("".charCodeAt),Vo=h("".replace),Go=h(1..toString),$o=/[\uD800-\uDFFF]/g,zo=/^[\uD800-\uDBFF]$/,Xo=/^[\uDC00-\uDFFF]$/,Yo=!ue||d(function(){var e=ne("Symbol")("stringify detection");return"[null]"!==qo([e])||"{}"!==qo({a:e})||"{}"!==qo(Object(e))}),Jo=d(function(){return'"\\udf06\\ud834"'!==qo("\udf06\ud834")||'"\\udead"'!==qo("\udead")}),Ko=function(e,t){var n=m(arguments),r=function(e){if(R(e))return e;if(Nt(e)){for(var t=e.length,r=[],n=0;n<t;n++){var o=e[n];"string"==typeof o?Bo(r,o):"number"!=typeof o&&"Number"!==z(o)&&"String"!==z(o)||Bo(r,O(o))}var i=r.length,a=!0;return function(e,t){if(a)return a=!1,t;if(Nt(this))return t;for(var n=0;n<i;n++)if(r[n]===e)return t}}}(t);if(R(r)||e!==undefined&&!ce(e))return n[1]=function(e,t){if(R(r)&&(t=P(r,this,Fo(e),t)),!ce(t))return t},_o(qo,null,n)},Qo=function(e,t,n){var r=Ho(n,t-1),n=Ho(n,t+1);return Do(zo,e)&&!Do(Xo,n)||Do(Xo,e)&&!Do(zo,r)?"\\u"+Go(Wo(e,0),16):e},no=(qo&&C({target:"JSON",stat:!0,arity:3,forced:Yo||Jo},{stringify:function(e,t,n){var r=m(arguments),r=_o(Yo?Ko:qo,null,r);return Jo&&"string"==typeof r?Vo(r,$o,Qo):r}}),d(function(){Rn(1)})),Zo=(C({target:"Object",stat:!0,forced:no,sham:!mn},{getPrototypeOf:function(e){return Rn(we(e))}}),C({target:"Object",stat:!0},{setPrototypeOf:Cn}),i.IteratorPrototype),r=s("toStringTag"),ei=k.set,a=function(r){var o=k.getterFor(r?"WrapForValidIterator":"IteratorHelper");return Mr(cn(Zo),{next:function(){var e=o(this);if(r)return e.nextHandler();try{var t=e.done?undefined:e.nextHandler();return Fn(t,e.done)}catch(N){throw e.done=!0,N}},"return":function(){var e,t=o(this),n=t.iterator;if(t.done=!0,r)return(e=pe(n,"return"))?P(e,n):Fn(undefined,!0);if(t.inner)try{yr(t.inner.iterator,"normal")}catch(N){return yr(n,"throw",N)}return n&&yr(n,"normal"),Fn(undefined,!0)}})},ti=a(!0),ni=a(!1),no=(Ge(ni,r,"Iterator Helper"),function(n,r){var e=function(e,t){t?(t.iterator=e.iterator,t.next=e.next):t=e,t.type=r?"WrapForValidIterator":"IteratorHelper",t.nextHandler=n,t.counter=0,t.done=!1,ei(this,t)};return e.prototype=r?ti:ni,e}),ri=function(e,t,n,r){try{return r?t(S(n)[0],n[1]):t(n)}catch(N){yr(e,"throw",N)}},oi=no(function(){for(var e,t=this.iterator,n=this.predicate,r=this.next;;){if(e=S(P(r,t)),this.done=!!e.done)return;if(ri(t,n,[e=e.value,this.counter++],!0))return e}}),ii=(C({target:"Iterator",proto:!0,real:!0,forced:!1},{filter:function(e){return S(this),he(e),new oi(eo(this),{predicate:e})}}),function(e,t){return(ii=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)}),f=function(){return(f=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function ai(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),o=0,t=0;t<n;t++)for(var i=arguments[t],a=0,u=i.length;a<u;a++,o++)r[o]=i[a];return r}var ui=/_?t(\d)?(imestamp)?=\d+&?/g,si=["aegis.qq.com","tamaegis.com","/aegis-sdk","rumt-","/flog.core.min.js","pingfore.qq.com","pingfore.tencent.com","zhiyan.tencent-cloud.net","h.trace.qq.com","btrace.qq.com","beacon.qq.com","dmplog.qq.com","qq.com/report","svibeacon.onezapp.com","cube.weixinbridge.com","doubleclick.net","pcmgrmonitor.3g.qq.com","tdm.qq.com","report.qqweb.qq.com","tpstelemetry.tencent.com","galileotelemetry.tencent.com","insight.cloud.tencent.com","facebook.com","facebook.net","google","yahoo.com","twitter.com","ga-audiences","report.idqqimg.com","arms-retcode.aliyuncs.com","px.effirst.com","sentry","baidu.com","hot-update.json","u.c.b.r.o.w.s.e.r","report.url.cn","sockjs-node","m3u8","flv"],ci=["ResizeObserver loop limit exceeded","ResizeObserver loop completed","Failed to execute 'transaction'","window.indexedDB.deleteDatabase is not a function"],fi=["ext1","ext2","ext3","level","trace","tag","seq","code"],li=["static","fetch"],di="unknown",hi=s("isConcatSpreadable"),mn=51<=ie||!d(function(){var e=[];return e[hi]=!1,e.concat()[0]!==e}),i=!mn||!u("concat"),pi=(C({target:"Array",proto:!0,arity:1,forced:i},{concat:function(e){for(var t,n,r,o,i,a=we(this),u=Kt(a,0),s=0,c=-1,f=arguments.length;c<f;c++)if(o=r=-1===c?a:arguments[c],i=void 0,!I(o)||((i=o[hi])!==undefined?!i:!Nt(o)))Lo(s+1),zr(u,s++,r);else for(n=gt(r),Lo(s+n),t=0;t<n;t++,s++)t in r&&zr(u,s,r[t]);return u.length=s,u}}),C({target:"Array",stat:!0},{isArray:Nt}),h([].join)),a=J!==Object||!$t("join",","),gi=(C({target:"Array",proto:!0,forced:a},{join:function(e){return pi(ee(this),e===undefined?",":e)}}),Zt.map),r=u("map"),yi=(C({target:"Array",proto:!0,forced:!r},{map:function(e){return gi(this,e,1<arguments.length?arguments[1]:undefined)}}),Zt.some),mn=$t("some"),i=(C({target:"Array",proto:!0,forced:!mn},{some:function(e){return yi(this,e,1<arguments.length?arguments[1]:undefined)}}),Date.prototype),vi=h(i.toString),mi=h(i.getTime),a=("Invalid Date"!==String(new Date(NaN))&&g(i,"toString",function(){var e=mi(this);return e==e?vi(this):"Invalid Date"}),or.f),u=d(function(){return!Object.getOwnPropertyNames(1)}),bi=(C({target:"Object",stat:!0,forced:u},{getOwnPropertyNames:a}),s("match")),wi=function(){var e=S(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t},Ei=RegExp.prototype,Ri=function(e){var t=e.flags;return t!==undefined||"flags"in Ei||L(e,"flags")||!re(Ei,e)?t:P(wi,e)},Si=x.RegExp,r=d(function(){var e=Si("a","y");return e.lastIndex=2,null!==e.exec("abcd")}),mn=r||d(function(){return!Si("a","y").sticky}),i={BROKEN_CARET:r||d(function(){var e=Si("^r","gy");return e.lastIndex=2,null!==e.exec("str")}),MISSED_STICKY:mn,UNSUPPORTED_Y:r},Oi=Ve.f,Ti=s("species"),xi=function(e){e=ne(e);A&&e&&!e[Ti]&&c(e,Ti,{configurable:!0,get:function(){return this}})},Ai=x.RegExp,Pi=d(function(){var e=Ai(".","s");return!(e.dotAll&&e.test("\n")&&"s"===e.flags)}),Ii=x.RegExp,Li=d(function(){var e=Ii("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}),u=Et.f,ki=k.enforce,Ci=s("match"),ji=x.RegExp,Ui=ji.prototype,Ni=x.SyntaxError,Mi=h(Ui.exec),_i=h("".charAt),Bi=h("".replace),Fi=h("".indexOf),qi=h("".slice),Di=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,Hi=/a/g,Wi=/a/g,a=new ji(Hi)!==Hi,Vi=i.MISSED_STICKY,Gi=i.UNSUPPORTED_Y,mn=A&&(!a||Vi||Pi||Li||d(function(){return Wi[Ci]=!1,ji(Hi)!==Hi||ji(Wi)===Wi||"/a/i"!==String(ji(Hi,"i"))}));if(kt("RegExp",mn)){for(var $i=function(e,t){var n,r,o=re(Ui,this),i=I(a=e)&&((i=a[bi])!==undefined?!!i:"RegExp"===z(a)),a=t===undefined,u=[],s=e;if(!o&&i&&a&&e.constructor===$i)return e;if((i||re(Ui,e))&&(e=e.source,a)&&(t=Ri(s)),e=e===undefined?"":O(e),t=t===undefined?"":O(t),s=e,i=t=Pi&&"dotAll"in Hi&&(n=!!t&&-1<Fi(t,"s"))?Bi(t,/s/g,""):t,Vi&&"sticky"in Hi&&(r=!!t&&-1<Fi(t,"y"))&&Gi&&(t=Bi(t,/y/g,"")),Li&&(e=(a=function(e){for(var t,n=e.length,r=0,o="",i=[],a=cn(null),u=!1,s=!1,c=0,f="";r<=n;r++){if("\\"===(t=_i(e,r)))t+=_i(e,++r);else if("]"===t)u=!1;else if(!u)switch(!0){case"["===t:u=!0;break;case"("===t:if(o+=t,"?:"===qi(e,r+1,r+3))continue;Mi(Di,qi(e,r+1))&&(r+=2,s=!0),c++;continue;case">"===t&&s:if(""===f||L(a,f))throw new Ni("Invalid capture group name");a[f]=!0,s=!(i[i.length]=[f,c]),f="";continue}s?f+=t:o+=t}return[o,i]}(e))[0],u=a[1]),a=Nr(ji(e,t),o?this:Ui,$i),(n||r||u.length)&&(t=ki(a),n&&(t.dotAll=!0,t.raw=$i(function(e){for(var t,n=e.length,r=0,o="",i=!1;r<=n;r++)"\\"!==(t=_i(e,r))?i||"."!==t?("["===t?i=!0:"]"===t&&(i=!1),o+=t):o+="[\\s\\S]":o+=t+_i(e,++r);return o}(e),i)),r&&(t.sticky=!0),u.length)&&(t.groups=u),e!==s)try{Ge(a,"source",""===s?"(?:)":s)}catch(N){}return a},zi=u(ji),Xi=0;zi.length>Xi;)!function(e,t,n){n in e||Oi(e,n,{configurable:!0,get:function(){return t[n]},set:function(e){t[n]=e}})}($i,ji,zi[Xi++]);(Ui.constructor=$i).prototype=Ui,g(x,"RegExp",$i,{constructor:!0})}xi("RegExp");var Yi=k.get,Ji=n("native-string-replace",String.prototype.replace),Ki=RegExp.prototype.exec,Qi=Ki,Zi=h("".charAt),ea=h("".indexOf),ta=h("".replace),na=h("".slice),ra=(r=/b*/g,P(Ki,a=/a/,"a"),P(Ki,r,"a"),0!==a.lastIndex||0!==r.lastIndex),oa=i.BROKEN_CARET,ia=/()??/.exec("")[1]!==undefined,aa=Qi=ra||ia||oa||Pi||Li?function(e){var t,n,r,o,i,a,u=this,s=Yi(u),e=O(e),c=s.raw;if(c)return c.lastIndex=u.lastIndex,l=P(Qi,c,e),u.lastIndex=c.lastIndex,l;var f=s.groups,c=oa&&u.sticky,l=P(wi,u),s=u.source,d=0,h=e;if(c&&(l=ta(l,"y",""),-1===ea(l,"g")&&(l+="g"),h=na(e,u.lastIndex),0<u.lastIndex&&(!u.multiline||(u.multiline,"\n"!==Zi(e,u.lastIndex-1)))&&(s="(?: "+s+")",h=" "+h,d++),t=new RegExp("^(?:"+s+")",l)),ia&&(t=new RegExp("^"+s+"$(?!\\s)",l)),ra&&(n=u.lastIndex),r=P(Ki,c?t:u,h),c?r?(r.input=na(r.input,d),r[0]=na(r[0],d),r.index=u.lastIndex,u.lastIndex+=r[0].length):u.lastIndex=0:ra&&r&&(u.lastIndex=u.global?r.index+r[0].length:n),ia&&r&&1<r.length&&P(Ji,r[0],t,function(){for(o=1;o<arguments.length-2;o++)arguments[o]===undefined&&(r[o]=undefined)}),r&&f)for(r.groups=i=cn(null),o=0;o<f.length;o++)i[(a=f[o])[0]]=r[a[1]];return r}:Qi;C({target:"RegExp",proto:!0,forced:/./.exec!==aa},{exec:aa});var ua=k.get,sa=RegExp.prototype,ca=TypeError;A&&i.MISSED_STICKY&&c(sa,"sticky",{configurable:!0,get:function(){if(this!==sa){if("RegExp"===z(this))return!!ua(this).sticky;throw new ca("Incompatible receiver, RegExp required")}}});fa=!1,(mn=/[ac]/).exec=function(){return fa=!0,/./.exec.apply(this,arguments)};var fa,u=!0===mn.test("abc")&&fa,la=/./.test,n=(C({target:"RegExp",proto:!0,forced:!u},{test:function(e){var t=S(this),e=O(e),n=t.exec;return R(n)?null!==(n=P(n,t,e))&&(S(n),!0):P(la,t,e)}}),Xe.PROPER),a=RegExp.prototype,da=a.toString,r=d(function(){return"/a/b"!==da.call({source:"a",flags:"b"})}),i=n&&"toString"!==da.name,ha=((r||i)&&g(a,"toString",function(){var e=S(this);return"/"+O(e.source)+"/"+O(Ri(e))},{unsafe:!0}),s("species")),pa=RegExp.prototype,mn=function(n,e,t,r){var a,o=s(n),u=!d(function(){var e={};return e[o]=function(){return 7},7!==""[n](e)}),i=u&&!d(function(){var e=!1,t=/a/;return"split"===n&&((t={}).constructor={},t.constructor[ha]=function(){return t},t.flags="",t[o]=/./[o]),t.exec=function(){return e=!0,null},t[o](""),!e});u&&i&&!t||(a=/./[o],i=e(o,""[n],function(e,t,n,r,o){var i=t.exec;return i===aa||i===pa.exec?u&&!o?{done:!0,value:P(a,t,n,r)}:{done:!0,value:P(e,n,t,r)}:{done:!1}}),g(String.prototype,n,i[0]),g(pa,o,i[1])),r&&Ge(pa[o],"sham",!0)},ga=o.charAt,ya=function(e,t,n){return t+(n?ga(e,t).length:1)},va=Math.floor,ma=h("".charAt),ba=h("".replace),wa=h("".slice),Ea=/\$([{AEGIS_INC}'`]|\d{1,2}|<[^>]*>)/g,Ra=/\$([{AEGIS_INC}'`]|\d{1,2})/g,Sa=TypeError,Oa=function(e,t){var n=e.exec;if(R(n))return null!==(n=P(n,e,t))&&S(n),n;if("RegExp"===z(e))return P(aa,e,t);throw new Sa("RegExp#exec called on incompatible receiver")},Ta=s("replace"),xa=Math.max,Aa=Math.min,Pa=h([].concat),Ia=h([].push),La=h("".indexOf),ka=h("".slice),u="$0"==="a".replace(/./,"$0"),Ca=!!/./[Ta]&&""===/./[Ta]("a","$0"),ja=(mn("replace",function(e,b,w){var E=Ca?"$":"$0";return[function(e,t){var n=Z(this),r=K(e)?undefined:pe(e,Ta);return r?P(r,e,n,t):P(b,O(n),e,t)},function(e,t){var n=S(this),r=O(e);if("string"==typeof t&&-1===La(t,E)&&-1===La(t,"$<")){e=w(b,n,r,t);if(e.done)return e.value}var o=R(t);o||(t=O(t));var i,a=n.global;a&&(i=n.unicode,n.lastIndex=0);for(var u,s=[];null!==(u=Oa(n,r))&&(Ia(s,u),a);)""===O(u[0])&&(n.lastIndex=ya(r,pt(n.lastIndex),i));for(var c,f="",l=0,d=0;d<s.length;d++){for(var h=O((u=s[d])[0]),p=xa(Aa(ct(u.index),r.length),0),g=[],y=1;y<u.length;y++)Ia(g,(c=u[y])===undefined?c:String(c));var v=u.groups,m=o?(m=Pa([h],g,p,r),v!==undefined&&Ia(m,v),O(_o(t,undefined,m))):function(i,a,u,s,c,e){var f=u+i.length,l=s.length,t=Ra;return c!==undefined&&(c=we(c),t=Ea),ba(e,t,function(e,t){var n;switch(ma(t,0)){case"$":return"$";case"&":return i;case"`":return wa(a,0,u);case"'":return wa(a,f);case"<":n=c[wa(t,1,-1)];break;default:var r,o=+t;if(0==o)return e;if(l<o)return 0!==(r=va(o/10))&&r<=l?s[r-1]===undefined?ma(t,1):s[r-1]+ma(t,1):e;n=s[o-1]}return n===undefined?"":n})}(h,r,p,g,v,t);l<=p&&(f+=ka(r,l,p)+m,l=p+h.length)}return f+ka(r,l)}]},!!d(function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})||!u||Ca),no(function(){var e=this.iterator,t=S(P(this.next,e));if(!(this.done=!!t.done))return ri(e,this.mapper,[t.value,this.counter++],!0)}));C({target:"Iterator",proto:!0,real:!0,forced:!1},{map:function(e){return S(this),he(e),new ja(eo(this),{mapper:e})}}),C({target:"Iterator",proto:!0,real:!0},{some:function(n){S(this),he(n);var e=eo(this),r=0;return wr(e,function(e,t){if(n(e,r++))return t()},{IS_RECORD:!0,INTERRUPTED:!0}).stopped}});_a.prototype.indexOf=function(e,t){for(var n=0;n<e.length;n++)if(e[n].callback===t)return n;return-1},_a.prototype.on=function(e,t,n){var r;if(void 0===n&&(n=0),this)return(r=this.eventsList[e])||(this.eventsList[e]=[],r=this.eventsList[e]),-1===this.indexOf(r,t)&&r.push({name:e,type:n||0,callback:t}),this},_a.prototype.one=function(e,t){this.on(e,t,1)},_a.prototype.remove=function(e,t){if(this){var n=this.eventsList[e];if(n){if(t)return n.length&&(t=this.indexOf(n,t),n.splice(t,1)),this;try{delete this.eventsList[e]}catch(B){}}return null}},_a.prototype.clear=function(){this.eventsList={}};var Ua,Na=_a,Ma=function(e){if(!e||0===e.length)return"{}";e=Array.isArray(e)?e:[e];var t=Object.keys(e[0]),n={};return t.forEach(function(t){n[t]=e.map(function(e){return e[t]})}),n.count=e.length,Ja(n)};function _a(){var a=this;this.emit=function(e,t){if(a){var n;if(null!=(r=a.eventsList[e])&&r.length)for(var r=r.slice(),o=0;o<r.length;o++){n=r[o];try{var i=n.callback.apply(a,[t]);if(1===n.type&&a.remove(e,n.callback),!1===i)break}catch(A){throw A}}return a}},this.eventsList={}}(n=Ua=Ua||{})[n.number=-1]="number",n.string="";var y,v,Ba,Fa=function(e,t){return"number"==typeof e||"string"==typeof e?e:t?Ua.string:Ua.number},qa=function(e,t){return"string"==typeof e?e.split("?")[t?1:0]||"":e},Da=function(e,t){return void 0===t&&(t=2048),String(e).replace(ui,"").slice(0,t)},Ha=function(e){return"string"==typeof e&&/^\//.test(e)?"https:"===(null==location?void 0:location.protocol):/^https/.test(e)},Wa=["application/xhtml+xml","application/xml","application/pdf","application/pkcs12","application/javascript","application/x-javascript","application/ecmascript","application/vnd.mspowerpoint","application/vnd.apple.mpegurl","application/ogg","text/css","text/javascript","image","audio","video","video/mp2t"],Va=/\.(json|js|css|jpg|jpeg|png|svg|apng|webp|gif|bmp|mp4|mp3|ts|mpeg|wav|webm|ogg|flv|m3u8|ttf|woff2|otf|eot|woff|html|htm|shtml|shtm|)$/i,Ga=["ret","retcode","code","errcode"],$a=function(e,t,n){var r,o,i;try{if("function"==typeof(null==t?void 0:t.retCodeHandler))return{code:void 0===(i=(o=t.retCodeHandler(e,null==n?void 0:n.url,null==n?void 0:n.ctx,null==n?void 0:n.payload)||{}).code)?di:i,isErr:o.isErr};if(!(e="string"==typeof e?JSON.parse(e):e))return{code:di,isErr:!1};"function"==typeof(null==(r=null==t?void 0:t.ret)?void 0:r.join)&&(Ga=[].concat(t.ret.map(function(e){return e.toLowerCase()})));var a=Object.getOwnPropertyNames(e).filter(function(e){return-1!==Ga.indexOf(e.toLowerCase())});return a.length?{code:""+(i="未知"!==(i=e[a[0]])&&""!==i?i:di),isErr:0!==i&&"0"!==i&&i!==di}:{code:di,isErr:!1}}catch(q){return{code:di,isErr:!1}}},za=function(e,t,n){try{var r="function"==typeof t?t(e,null==n?void 0:n.url)||"":e;return Ka(r).slice(0,102400)}catch(B){return""}},Xa=function(){var n=new WeakSet;return function(e,t){if(t instanceof Error)return"Error.message: "+t.message+" \n  Error.stack: "+t.stack;if("object"===vo(t)&&null!==t){if(n.has(t))return"[Circular "+(e||"root")+"]";n.add(t)}return t}},Ya=function(e){if("string"==typeof e)return e;try{return e instanceof Error?(JSON.stringify(e,Xa(),4)||"undefined").replace(/"/gim,""):JSON.stringify(e,Xa(),4)||"undefined"}catch(t){return"error happen when aegis stringify: \n "+t.message+" \n "+t.stack}},Ja=function(e){if("string"==typeof e)return e;try{return JSON.stringify(e,Xa())||"undefined"}catch(t){return"error happen when aegis stringify: \n "+t.message+" \n "+t.stack}},Ka=function(n,r){void 0===r&&(r=3);var o,i,a,u="";return Array.isArray(n)?(u+="[",o=n.length,n.forEach(function(e,t){"object"===vo(e)&&1<r?u+=Ka(e,r-1):u+=Za(e),u+=t===o-1?"":","}),u+="]"):n instanceof Object?(u="{",i=Object.keys(n),a=i.length,i.forEach(function(e,t){"object"===vo(n[e])&&1<r?u+='"'+e+'":'+Ka(n[e],r-1):u+=Qa(e,n[e]),u+=t===a-1||t<a-1&&"undefined"==typeof n[i[t+1]]?"":","}),u+="}"):u+=n,u},Qa=function(e,t){var n=vo(t),r="";return"string"===n||"object"===n?r+='"'+e+'":"'+t+'"':"function"==typeof t?r+='"'+e+'":"function '+t.name+'"':"symbol"===vo(t)?r+='"'+e+'":"symbol"':"number"!=typeof t&&"boolean"!==n||(r+='"'+e+'": '+t),r},Za=function(e){var t=vo(e);return""+("undefined"===t||"symbol"===t||"function"===t?"null":"string"===t||"object"===t?'"'+e+'"':e)},eu=/data:(image|text|application|font)\/.*;base64/,tu=function(t,e){return"string"!=typeof t||!t||e&&-1<t.indexOf(e)||eu.test(t)||si.some(function(e){return-1<t.indexOf(e)})},nu=((r=y=y||{}).INFO_ALL="-1",r.API_RESPONSE="1",r.INFO="2",r.ERROR="4",r.PROMISE_ERROR="8",r.AJAX_ERROR="16",r.SCRIPT_ERROR="32",r.IMAGE_ERROR="64",r.CSS_ERROR="128",r.CONSOLE_ERROR="256",r.MEDIA_ERROR="512",r.RET_ERROR="1024",r.REPORT="2048",r.PV="4096",r.EVENT="8192",r.PAGE_NOT_FOUND_ERROR="16384",r.WEBSOCKET_ERROR="32768",r.BRIDGE_ERROR="65536",r.LAZY_LOAD_ERROR="131072",0,0,(i=v=v||{}).LOG="log",i.SPEED="speed",i.PERFORMANCE="performance",i.OFFLINE="offline",i.WHITE_LIST="whiteList",i.VITALS="vitals",i.PV="pv",i.CUSTOM_PV="customPV",i.EVENT="event",i.CUSTOM="custom",i.SDK_ERROR="sdkError",i.SET_DATA="setData",i.LOAD_PACKAGE="loadPackage",(a=Ba=Ba||{}).production="production",a.development="development",a.gray="gray",a.pre="pre",a.daily="daily",a.local="local",a.test="test",a.others="others",TypeError),ru="Reduce of empty array with no initial value",u=function(c){return function(e,t,n,r){var o=we(e),i=J(o),a=gt(o);if(he(t),0===a&&n<2)throw new nu(ru);var u=c?a-1:0,s=c?-1:1;if(n<2)for(;;){if(u in i){r=i[u],u+=s;break}if(u+=s,c?u<0:a<=u)throw new nu(ru)}for(;c?0<=u:u<a;u+=s)u in i&&(r=t(r,i[u],u,o));return r}},no={left:u(!1),right:u(!0)},n=function(e){return oe.slice(0,e.length)===e},ou=n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":x.Bun&&"string"==typeof Bun.version?"BUN":x.Deno&&"object"==typeof Deno.version?"DENO":"process"===z(x.process)?"NODE":x.window&&x.document?"BROWSER":"REST",iu="NODE"==ou,au=no.left,r=!iu&&79<ie&&ie<83||!$t("reduce"),uu=(C({target:"Array",proto:!0,forced:r},{reduce:function(e){var t=arguments.length;return au(this,e,t,1<t?arguments[1]:undefined)}}),TypeError),su=(C({target:"Iterator",proto:!0,real:!0},{reduce:function(t){S(this),he(t);var e=eo(this),n=arguments.length<2,r=n?undefined:arguments[1],o=0;if(wr(e,function(e){r=n?(n=!1,e):t(r,e,o),o++},{IS_RECORD:!0}),n)throw new uu("Reduce of empty iterator with no initial value");return r}}),C({target:"Iterator",proto:!0,real:!0},{find:function(n){S(this),he(n);var e=eo(this),r=0;return wr(e,function(e,t){if(n(e,r++))return t(e)},{IS_RECORD:!0,INTERRUPTED:!0}).result}}),TypeError),cu=function(e,t){if(e<t)throw new su("Not enough arguments");return e},fu=x.Function,lu=/MSIE .\./.test(oe)||"BUN"==ou&&((a=x.Bun.version.split(".")).length<3||"0"===a[0]&&(a[1]<3||"3"===a[1]&&"0"===a[2])),u=function(i,a){var u=a?2:1;return lu?function(e,t){var n=cu(arguments.length,1)>u,r=R(e)?e:fu(e),o=n?m(arguments,u):[],e=n?function(){_o(r,this,o)}:r;return a?i(e,t):i(e)}:i},n=u(x.setInterval,!0),r=(C({global:!0,bind:!0,forced:x.setInterval!==n},{setInterval:n}),u(x.setTimeout,!0)),du=(C({global:!0,bind:!0,forced:x.setTimeout!==r},{setTimeout:r}),function(e){return e.filter(function(n,r){return"static"!==n.type||!e.find(function(e,t){return n.url===e.url&&200===n.status&&r<t})})}),hu=function(n,r){var o,i=[],a=n.config;return n.lifeCycle.on("destroy",function(){i.length=0}),function(e,t){Array.isArray(e)?i=i.concat(e):i.push(e),r&&i.length>=r||n.sendNow&&0<i.length?(i=du(i),t(i.splice(0,i.length)),o&&clearTimeout(o)):(o&&clearTimeout(o),o=setTimeout(function(){o=null,0<(i=du(i)).length&&t(i.splice(0,i.length))},a.delay))}},pu=function(e,t){return Array.isArray(e)?t(e.map(function(e){return t=f(f({},e),{msg:"string"==typeof e.msg?e.msg:[].concat(e.msg).map(Ya).join(" ")}),fi.forEach(function(e){t[e]||delete t[e]}),t;var t})):t([f(f({},e),{msg:"string"==typeof e.msg?e.msg:Ya(e.msg)})])},gu=function(e){e.level===y.INFO_ALL&&(e.level=y.INFO)},yu=RangeError,a=function(e){var t=O(Z(this)),n="",r=ct(e);if(r<0||r===Infinity)throw new yu("Wrong number of repetitions");for(;0<r;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n},vu=(C({target:"String",proto:!0},{repeat:a}),{}),mu={},bu=function(e){return vu[e]||(vu[e]=setTimeout(function(){mu[e]={},vu[e]=null},6e4)),vu[e]},wu=function(e){return(Array.isArray(e)?e:[e]).map(function(n){return Object.getOwnPropertyNames(n).reduce(function(e,t){return"ctx"!==t&&(e[t]=n[t]),e},{level:y.INFO,msg:""})})},Eu=function(r){return function(e){return r.sendPipeline([function(e,n){return n({url:r.config.url||"",data:Ma(wu(e)),method:"post",contentType:"application/json",type:v.LOG,log:e,requestConfig:{timeout:5e3},success:function(){var t=r.config.onReport;"function"==typeof t&&e.forEach(function(e){t(e)}),"function"==typeof n&&n([])}})}],v.LOG)(e)}},Ru=function(s,c){return function(e,t){var n,r,o,i=Array.isArray(e),a=i?e:[e],u=(s.lifeCycle.emit("beforeRequest",e),s.config.beforeRequest);(a="function"==typeof u?a.map(function(e){try{var t=u({logs:e,logType:c});return(null==t?void 0:t.logType)===c&&null!=t&&t.logs?t.logs:!1!==t&&e}catch(N){return e}}).filter(function(e){return!1!==e}):a).length&&(n=a,e=fi,!Array.isArray(n)||n.length<=1||(r=[],o=[],!(o="string"==typeof e?[e]:e))||o.length<=0||(o.forEach(function(t){n.forEach(function(e){null!=e&&e[t]&&r.push(t)})}),0<r.length&&(n=n.map(function(e){var t={};return r.forEach(function(e){t[e]=""}),f(f({},t),e)}))),a=n,t(i?a:a[0]))}},Su=function(o){return function(e,t){o.lifeCycle.emit("modifyRequest",e);var n=o.config.modifyRequest;if("function"==typeof n)try{var r=n(e);"object"===vo(r)&&"url"in r&&(e=r)}catch(N){console.error(N)}t(e)}},Ou=function(r){return function(e,t){null!=(n=r.lifeCycle)&&n.emit("afterRequest",e);var n=(r.config||{}).afterRequest;"function"==typeof n&&!1===n(e)||t(e)}},n=x,Tu=h(1..valueOf),u="\t\n\x0B\f\r                　\u2028\u2029\ufeff",xu=h("".replace),Au=RegExp("^["+u+"]+"),Pu=RegExp("(^|[^"+u+"])["+u+"]+$"),r=function(t){return function(e){e=O(Z(e));return 1&t&&(e=xu(e,Au,"")),e=2&t?xu(e,Pu,"$1"):e}},r={start:r(1),end:r(2),trim:r(3)},Iu=Et.f,Lu=Be.f,ku=Ve.f,Cu=r.trim,ju=x.Number,Uu=(n.Number,ju.prototype),Nu=x.TypeError,Mu=h("".slice),_u=h("".charCodeAt),Bu=function(e){var t,n,r,o,i,a,u,s=ke(e,"number");if(ce(s))throw new Nu("Cannot convert a Symbol value to a number");if("string"==typeof s&&2<s.length)if(s=Cu(s),43===(e=_u(s,0))||45===e){if(88===(t=_u(s,2))||120===t)return NaN}else if(48===e){switch(_u(s,1)){case 66:case 98:n=2,r=49;break;case 79:case 111:n=8,r=55;break;default:return+s}for(i=(o=Mu(s,2)).length,a=0;a<i;a++)if((u=_u(o,a))<48||r<u)return NaN;return parseInt(o,n)}return+s},l=kt("Number",!ju(" 0o1")||!ju("0b1")||ju("+0x1")),Fu=function(e){var t,e=arguments.length<1?0:ju(function(e){e=ke(e,"number");return"bigint"==typeof e?e:Bu(e)}(e));return re(Uu,t=this)&&d(function(){Tu(t)})?Nr(Object(e),this,Fu):e};if(Fu.prototype=Uu,l&&(Uu.constructor=Fu),C({global:!0,constructor:!0,wrap:!0,forced:l},{Number:Fu}),l)for(var qu=n.Number,Du=ju,Hu,Wu=A?Iu(Du):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),Vu=0;Wu.length>Vu;Vu++)L(Du,Hu=Wu[Vu])&&!L(qu,Hu)&&ku(qu,Hu,Lu(Du,Hu));mn("match",function(r,u,s){return[function(e){var t=Z(this),n=K(e)?undefined:pe(e,r);return n?P(n,e,t):new RegExp(e)[r](O(t))},function(e){var t=S(this),n=O(e),e=s(u,t,n);if(e.done)return e.value;if(!t.global)return Oa(t,n);for(var r=t.unicode,o=[],i=t.lastIndex=0;null!==(a=Oa(t,n));){var a=O(a[0]);""===(o[i]=a)&&(t.lastIndex=ya(n,pt(t.lastIndex),r)),i++}return 0===i?null:o}]});var Gu={generateTraceId:zu(16),generateSpanId:zu(8)},$u=Array(32);function zu(t){return function(){for(var e=0;e<2*t;e++)$u[e]=Math.floor(16*Math.random())+48,58<=$u[e]&&($u[e]+=39);return String.fromCharCode.apply(null,$u.slice(0,2*t))}}function Xu(){return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,function(e){return(e^(16*Math.random()&15)>>e/4).toString(16)})}var Yu=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})},Ju="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Ku=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Qu=function(e){for(var t,n,r,o="",i=0,a=(e=String(e)).length%3;i<e.length;){if(255<(t=e.charCodeAt(i++))||255<(n=e.charCodeAt(i++))||255<(r=e.charCodeAt(i++)))throw new TypeError("Failed to execute 'btoa': The string to be encoded contains characters outside of the Latin1 range.");o+=Ju.charAt((t=t<<16|n<<8|r)>>18&63)+Ju.charAt(t>>12&63)+Ju.charAt(t>>6&63)+Ju.charAt(63&t)}return a?o.slice(0,a-3)+"===".substring(a):o},Zu=function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Ku.test(e))throw new TypeError("Failed to execute 'atob': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,o="",i=0;i<e.length;)t=Ju.indexOf(e.charAt(i++))<<18|Ju.indexOf(e.charAt(i++))<<12|(n=Ju.indexOf(e.charAt(i++)))<<6|(r=Ju.indexOf(e.charAt(i++))),o+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o},es=(is.prototype.generate=function(e,t,n){if(void 0===t&&(t={}),this.url=e,!this.isUrlIgnored()&&this.isUrlInTraceUrls()&&this.traceType){switch(this.traceType){case"traceparent":this.traceId=this.createTraceparent();break;case"b3":this.traceId=this.createB3();break;case"sw8":this.traceId=this.createSw8(n);break;case"sentry-trace":this.traceId=this.createSentryTrace();break;default:return console.warn("this trace key "+this.traceType+" is not supported"),void(this.traceId="")}return t[this.traceType]&&(this.traceId=t[this.traceType]),{name:this.traceType,value:this.traceId}}},is.prototype.createTraceparent=function(){var e=Gu.generateSpanId();return"00-"+Gu.generateTraceId()+"-"+e+"-0"+this.traceFlag.toString(16)},is.prototype.createB3=function(){var e=Gu.generateSpanId();return Gu.generateTraceId()+"-"+e+"-"+this.traceFlag},is.prototype.createSw8=function(e){var t="function"==typeof btoa?btoa:Qu,e=e||{},n=e.host,n=void 0===n?"":n,e=e.pathname,e=void 0===e?"":e,r=Yu(),o=Yu();return"1-"+String(t(o))+"-"+String(t(r))+"-1-"+String(t("aegis"))+"-"+String(t("1.43.19"))+"-"+String(t(encodeURI(e)))+"-"+String(t(n))},is.prototype.createSentryTrace=function(){var e=Xu().substring(16);return Xu()+"-"+e+"-"+this.traceFlag},is.prototype.isUrlIgnored=function(){if(Array.isArray(this.ignoreUrls)&&0!==this.ignoreUrls.length)for(var e=0,t=this.ignoreUrls;e<t.length;e++){var n=t[e];if(this.urlMatches(this.url,n))return!0}return!1},is.prototype.isUrlInTraceUrls=function(){if(!this.urls)return!0;if(Array.isArray(this.urls)){if(0===this.urls.length)return!1;for(var e=0,t=this.urls;e<t.length;e++){var n=t[e];if(this.urlMatches(this.url,n))return!0}}return!1},is.prototype.urlMatches=function(e,t){return"string"==typeof t?e===t:!!e.match(t)},is),ts={sw8:function(e){var t="function"==typeof atob?atob:Zu,e=e.split("-")[1];return e?t(e):""},traceparent:function(e){return e.split("-")[1]},b3:function(e){return e.split("-")[0]},"sentry-trace":function(e){return e.split("-")[0]}},ns=function(e){var t,n="";return n="object"===vo(e)&&(t=(e=function(e,t){for(var n=0;n<t.length;n++){var r=t[n],o=e[r]||"function"==typeof e.get&&e.get(r);if(o)return[r,o]}return["",""]}(e,Object.keys(ts)))[0])?ts[t](e[1]):n},rs=function(){},os=function(n){if(n&&n.reduce&&n.length)return 1===n.length?function(e,t){n[0](e,t||rs)}:n.reduce(function(n,r){return function(e,t){return void 0===t&&(t=rs),n(e,function(e){return null==r?void 0:r(e,t)})}});throw new TypeError("createPipeline need at least one function param")};function is(e,t,n,r){void 0===n&&(n=null),this.traceType=e,this.ignoreUrls=t,this.urls=n,this.traceFlag=r===undefined||null===r?1:Number(!!r)}C({target:"Object",stat:!0,sham:!A},{getOwnPropertyDescriptors:function(e){for(var t,n,r=ee(e),o=Be.f,i=Ot(r),a={},u=0;i.length>u;)(n=o(r,t=i[u++]))!==undefined&&zr(a,t,n);return a}});var as=function(t,n){Object.getOwnPropertyNames(t).forEach(function(e){"function"==typeof t[e]&&"constructor"!==e&&(n?n[e]="sendPipeline"===e?function(){return function(){}}:function(){}:t[e]=function(){})})},l=(b.use=function(e){-1===b.installedPlugins.indexOf(e)&&e.aegisPlugin&&b.installedPlugins.push(e)},b.unuse=function(e){e=b.installedPlugins.indexOf(e);-1!==e&&b.installedPlugins.splice(e,1)},b.prototype.init=function(e){this.setConfig(e);for(var t=0;t<b.installedPlugins.length;t++)try{b.installedPlugins[t].patch(this)}catch(_){this.sendSDKError(_)}this.lifeCycle.emit("onInited")},b.prototype.destroy=function(e){void 0===e&&(e=!1);var t,n,r=b.instances.indexOf(this);-1!==r&&b.instances.splice(r,1);for(var o=b.installedPlugins.length-1;0<=o;o--)try{b.installedPlugins[o].unpatch(this)}catch(D){this.sendSDKError(D)}if(this.lifeCycle.emit("destroy"),this.lifeCycle.clear(),e)t=this,n=Object.getOwnPropertyDescriptors(t),Object.keys(n).forEach(function(e){n[e].writable&&(t[e]=null)}),Object.setPrototypeOf(this,null);else{for(var i=this;i.constructor!==Object&&as(i,this),i=Object.getPrototypeOf(i););0===b.instances.length&&(r=Object.getPrototypeOf(this).constructor,as(r),as(b))}},b.prototype.setConfig=function(e){Object.assign(this.config,e);var e=this.config,t=e.id,n=e.uin,r=e.version,o=e.ext1,i=e.ext2,a=e.ext3,u=e.aid,s=e.env,c=void 0===s?"production":s,s=e.pageUrl,e=this.bean.id!==t||this.bean.uin!==n||this.bean.aid!==u;return this.bean.id=t||"",this.bean.uin=n||"",this.bean.version=r||"1.43.19",this.bean.aid=u||"",this.bean.env=function(){switch(c){case Ba.production:case Ba.development:case Ba.gray:case Ba.pre:case Ba.daily:case Ba.local:case Ba.test:case Ba.others:return 1;default:return}}()?c:Ba.others,s&&this.extendBean("from",encodeURIComponent(s.slice(0,2048))),o&&this.extendBean("ext1",encodeURIComponent(o)),i&&this.extendBean("ext2",encodeURIComponent(i)),a&&this.extendBean("ext3",encodeURIComponent(a)),e&&this.lifeCycle.emit("onConfigChange",this.config),this.config},b.prototype.extendBean=function(e,t){this.bean[e]=t},b.prototype.send=function(e,o,i){var t=this;return os([Su(this),function(n,r){t.request(n,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r({isErr:!1,result:e,logType:n.type,logs:n.log}),null!=o&&o.apply(void 0,e)},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r({isErr:!0,result:e,logType:n.type,logs:n.log}),null!=i&&i.apply(void 0,e)})},Ou(this)])(e)},b.prototype.sendSDKError=function(e){var n=this;this.sendPipeline([function(e,t){t({url:n.config.url+"?id=1085&msg[0]="+encodeURIComponent(Ya(e))+"&level[0]=2&from="+n.config.id+"&count=1&version="+n.config.id+"(1.43.19)",addBean:!1,method:"get",type:v.SDK_ERROR,log:e})}],v.SDK_ERROR)(e)},b.prototype.sendPipeline=function(e,t){var n,i=this;return os(ai([function(e,t){if("number"!=typeof n.config.random&&(console.warn("random must in [0, 1], default is 1."),n.config.random=1),!n.isHidden||!n.isGetSample)if(n.isGetSample)n.isHidden||t(e);else{if(n.isGetSample=!0,Math.random()<n.config.random)return n.isHidden=!1,t(e);n.isHidden=!0}},Ru(n=this,t)],e,[Su(this),function(r,o){i.request(r,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=!1;-1<(""+e[i.failRequestCount=0]).indexOf("403 forbidden")&&(n=!0,i.destroy()),o({isErr:n,result:e,logType:null==r?void 0:r.type,logs:null==r?void 0:r.log}),null!=(n=null==r?void 0:r.success)&&n.call.apply(n,ai([r],e))},function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];60<=++i.failRequestCount&&i.destroy(),-1<(""+t[0]).indexOf("403 forbidden")&&i.destroy(),o({isErr:!0,result:t,logType:null==r?void 0:r.type,logs:null==r?void 0:r.log}),null!=(e=null==r?void 0:r.fail)&&e.call.apply(e,ai([r],t))})},Ou(this)]))},b.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:y.INFO,msg:e};1===e.length&&e[0].msg&&Object.assign(n,f({},e[0]),{level:y.INFO}),this.normalLogPipeline(n)},b.prototype.infoAll=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:y.INFO_ALL,msg:e};1===e.length&&e[0].msg&&Object.assign(n,f({},e[0]),{level:y.INFO_ALL}),this.normalLogPipeline(n)},b.prototype.report=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:y.REPORT,msg:e};1===e.length&&e[0].msg&&Object.assign(n,f({},e[0])),this.normalLogPipeline(n)},b.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={level:y.ERROR,msg:e};1===e.length&&e[0].msg&&Object.assign(n,f({},e[0]),{level:y.ERROR}),this.normalLogPipeline(n)},b.prototype.reportEvent=function(e){e&&((e="string"==typeof e?{name:e,ext1:this.config.ext1||"",ext2:this.config.ext2||"",ext3:this.config.ext3||""}:e).name?("string"!=typeof e.name&&(console.warn("reportEvent params name must be string"),e.name=String(e.name)),this.eventPipeline(e)):console.warn("reportEvent params error"))},b.prototype.reportT=function(e){var t=e.name,n=e.duration,r=e.ext1,r=void 0===r?"":r,o=e.ext2,o=void 0===o?"":o,i=e.ext3,i=void 0===i?"":i,e=e.from;if("string"==typeof t&&"number"==typeof n&&"string"==typeof r&&"string"==typeof o&&"string"==typeof i){if(!(n<0||6e4<n))return this.submitCustomTime(t,n,r,o,i,void 0===e?"":e);console.warn("reportTime: duration must between 0 and 60000")}else console.warn("reportTime: params error")},b.prototype.reportTime=function(e,t){if("object"===vo(e))return this.reportT(e);"string"==typeof e?"number"==typeof t?t<0||6e4<t?console.warn("reportTime: duration must between 0 and 60000"):this.submitCustomTime(e,t):console.warn("reportTime: second param must be number"):console.warn("reportTime: first param must be a string")},b.prototype.time=function(e){"string"==typeof e?this.timeMap[e]?console.warn("Timer "+e+" already exists"):this.timeMap[e]=Date.now():console.warn("time: first param must be a string")},b.prototype.timeEnd=function(e){"string"==typeof e?this.timeMap[e]?(this.submitCustomTime(e,Date.now()-this.timeMap[e]),delete this.timeMap[e]):console.warn("Timer "+e+" does not exist"):console.warn("timeEnd: first param must be a string")},b.prototype.ready=function(e,t,n){throw new Error('You need to override "ready" method')},b.prototype.request=function(e,t,n){throw new Error('You need to override "request" method')},b.prototype.speedLogPipeline=function(e){throw new Error('You need to override "speedLogPipeline" method')},Object.defineProperty(b.prototype,"__version__",{get:function(){return"1.43.19"},enumerable:!1,configurable:!0}),Object.defineProperty(b.prototype,"LogType",{get:function(){return y},enumerable:!1,configurable:!0}),b.prototype.reportPv=function(e){e&&console.warn("reportPv is deprecated, please use reportEvent")},b.prototype.submitCustomTime=function(e,t,n,r,o,i){this.customTimePipeline({name:e,duration:t,ext1:n||this.config.ext1,ext2:r||this.config.ext2,ext3:o||this.config.ext3,from:i||undefined})},b.version="1.43.19",b.instances=[],b.logType=y,b.environment=Ba,b.installedPlugins=[],b),us=Function,ss=h([].concat),cs=h([].join),fs={},n=q?us.bind:function(t){var n=he(this),e=n.prototype,r=m(arguments,1),o=function(){var e=ss(r,m(arguments));return this instanceof o?function(e,t,n){if(!L(fs,t)){for(var r=[],o=0;o<t;o++)r[o]="a["+o+"]";fs[t]=us("C,a","return new C("+cs(r,",")+")")}return fs[t](e,n)}(n,e.length,e):n.apply(t,e)};return I(e)&&(o.prototype=e),o};function b(e){var n,t,r,o,a,i,u,s,c,f,l,d,h,p,g=this;this.isGetSample=!1,this.isHidden=!1,this.config={version:0,delay:1e3,onError:!0,repeat:60,random:1,aid:!0,device:!0,pagePerformance:!0,webVitals:!0,speedSample:!0,onClose:!0,reportLoadPackageSpeed:!0,hostUrl:"https://aegis.qq.com",env:"production",url:"",offlineUrl:"",whiteListUrl:"",pvUrl:"",speedUrl:"",customTimeUrl:"",performanceUrl:"",performanceUrlForHippy:"",webVitalsUrl:"",eventUrl:"",setDataReportUrl:"",reportImmediately:!0},this.isWhiteList=!1,this.lifeCycle=new Na,this.bean={},this.normalLogPipeline=os([hu(this,5),pu,function(e,t){var r=n.config;t(e=e.map(function(e){var t,n=r.maxLength||102400;try{if(!e.msg||e.msg.length<=n)return e;e.msg=null==(t=e.msg)?void 0:t.substring(0,n)}catch(B){e.msg=Ya(e.msg).substring(0,r.maxLength)}return e}))},(p=(n=this).config,function(e,t){var n="number"==typeof p.repeat?p.repeat:60;if(n<=0)return t(e);var r=(null==p?void 0:p.id)+"_error",o=mu[r]||{};t(e.filter(function(e){if(e.level===y.ERROR||e.level===y.PROMISE_ERROR||e.level===y.AJAX_ERROR||e.level===y.SCRIPT_ERROR||e.level===y.IMAGE_ERROR||e.level===y.CSS_ERROR||e.level===y.MEDIA_ERROR||e.level===y.RET_ERROR||e.level===y.BRIDGE_ERROR||e.level===y.PAGE_NOT_FOUND_ERROR||e.level===y.WEBSOCKET_ERROR||e.level===y.LAZY_LOAD_ERROR){e=e.msg.slice(0,200);if(o[e]>n)return vu[r]||bu(r),!1;o[e]=1+~~o[e],mu[r]=o}return!0}))}),(d=this.lifeCycle.emit,h=this.config,function(e,t){var n,r=h.logCreated;return"function"==typeof r?(n=e.filter(function(e){return!1!==r(e)}),d("beforeWrite",n),t(n)):(d("beforeWrite",e),t(e))}),(l=this,setTimeout(function(){var e=l.config,t=e.pvUrl,n=void 0===t?"":t,t=e.spa,e=-1<["web-sdk","mp-sdk"].indexOf("web-sdk");n&&(e&&!t||!e)&&l.sendPipeline([function(e,t){t({url:n,type:v.PV})}],v.PV)(null)},100),function(e,t){t(e)}),(c=s=u=!1,f=[],(a=this).lifeCycle.on("onConfigChange",function(){i&&clearTimeout(i),i=setTimeout(function(){var e,n;!c&&a.config&&(c=!0,e=a.config.whiteListUrl,(n=void 0===e?"":e)&&a.sendPipeline([function(e,t){t({url:n,type:v.WHITE_LIST,success:function(e){s=!0;try{var t=e.data||JSON.parse(e),n=t.retcode,r=t.result,o=void 0===r?{}:r,i=(0===n&&(u=o.is_in_white_list,a.isWhiteList=u,0<=o.rate)&&o.rate<=1&&(a.config.random=o.rate,a.isGetSample=!1),a.isWhiteList&&f.length?Eu(a)(f.splice(0),function(){}):!a.isWhiteList&&f.length&&(f.length=0),a.config.onWhitelist);"function"==typeof i&&i(u)}catch(P){}},fail:function(){s=!0}})}],v.WHITE_LIST)(null),c=!1)},a.config.uin?50:500)}),a.lifeCycle.on("destroy",function(){f.length=0}),function(e,t){var n;u||null!=(n=null==(n=a.config)?void 0:n.api)&&n.reportRequest?t(e.concat(f.splice(0)).map(function(e){return gu(e),e})):(n=e.filter(function(e){return e.level!==y.INFO&&e.level!==y.API_RESPONSE?(gu(e),!0):(s||(f.push(e),200<=f.length&&(f.length=200)),!1)})).length&&t(n)}),function(e,t){try{var n=JSON.parse(JSON.stringify(e)),r=(g.lifeCycle.emit("beforeReport",n),g.config.beforeReport);(e="function"==typeof r?e.filter(function(e){return!1!==r(e)}):e).length&&t(e)}catch(x){}},Eu(this)]),this.eventPipeline=os([hu(this,10),(o=this,function(e){o.sendPipeline([function(e,t){var n=e.map(function(e){return{name:e.name,ext1:e.ext1||o.config.ext1||"",ext2:e.ext2||o.config.ext2||"",ext3:e.ext3||o.config.ext3||""}});t({url:o.config.eventUrl+"?payload="+encodeURIComponent(JSON.stringify(n)),type:v.EVENT,log:e})}],v.EVENT)(e)})]),this.customTimePipeline=os([hu(this,10),(r=this,function(e){return r.sendPipeline([function(e,t){t({url:r.config.customTimeUrl+"?payload="+encodeURIComponent(JSON.stringify({custom:e})),type:v.CUSTOM,log:e})}],v.CUSTOM)(e)})]),this.timeMap={},this.failRequestCount=0,this.config=(t=this.config,"string"!=typeof(e=void 0===(e=e.hostUrl)?"https://aegis.qq.com":e)&&(e="https://aegis.qq.com"),t.url=t.url||e+"/collect",t.offlineUrl=t.offlineUrl||e+"/offline",t.whiteListUrl=t.whiteListUrl||e+"/collect/whitelist",t.pvUrl=t.pvUrl||e+"/collect/pv",t.eventUrl=t.eventUrl||e+"/collect/events",t.speedUrl=t.speedUrl||e+"/speed",t.customTimeUrl=t.customTimeUrl||e+"/speed/custom",t.performanceUrl=t.performanceUrl||e+"/speed/performance",t.performanceUrlForHippy=t.performanceUrlForHippy||e+"/speed/hippyPerformance",t.webVitalsUrl=t.webVitalsUrl||e+"/speed/webvitals",t.setDataReportUrl=t.SetDataReportUrl||e+"/speed/miniProgramData",t),b.instances.push(this)}C({target:"Function",proto:!0,forced:Function.bind!==n},{bind:n});Es.prototype.patch=function(e){this.canUse(e)&&this.exist(e)&&(this.instances.push(e),this.triggerInit(e),this.triggerOnNewAegis(e))},Es.prototype.unpatch=function(e){var t=this.instances.indexOf(e);-1!==t&&(this.instances.splice(t,1),0===this.instances.length)&&this.uninstall(e)},Es.prototype.countInstance=function(){return this.instances.length},Es.prototype.uninstall=function(e){var t;null!=(t=null==(t=this.option)?void 0:t.destroy)&&t.apply(this,[e])},Es.prototype.walk=function(n){var r=this;this.instances.forEach(function(e){var t=r.canUse(e);t&&n(e,t)})},Es.prototype.canUse=function(e){e=this.getConfig(e);return!(!e||"object"!==vo(e))||!!e},Es.prototype.getConfig=function(e){return null==(e=e.config)?void 0:e[this.name]},Es.prototype.exist=function(e){return-1===this.instances.indexOf(e)},Es.prototype.triggerInit=function(e){var t;this.inited||(this.inited=!0,null==(t=null==(t=this.option)?void 0:t.init))||t.call(this.option,this.getConfig(e))},Es.prototype.triggerOnNewAegis=function(e){var t;null!=(t=null==(t=this.option)?void 0:t.onNewAegis)&&t.call(this.option,e,this.getConfig(e))};var Iu=Es,mn=new Iu({name:"aid",aid:"",init:function(e){try{var t=!0!==e&&e||window.localStorage.getItem("AEGIS_ID");t||(t=Yu(),window.localStorage.setItem("AEGIS_ID",t)),this.aid=t}catch(M){}},onNewAegis:function(e){e.bean.aid=this.aid,e.config.aid=this.aid}}),ls=RangeError,ds=String,hs=Math.floor,ps=h(a),gs=h("".slice),ys=h(1..toFixed),vs=function(e,t,n){return 0===t?n:t%2==1?vs(e,t-1,n*e):vs(e*e,t/2,n)},ms=function(e,t,n){for(var r=-1,o=n;++r<6;)o+=t*e[r],e[r]=o%1e7,o=hs(o/1e7)},bs=function(e,t){for(var n=6,r=0;0<=--n;)r+=e[n],e[n]=hs(r/t),r=r%t*1e7},ws=function(e){for(var t,n=6,r="";0<=--n;)""===r&&0!==n&&0===e[n]||(t=ds(e[n]),r=""===r?t:r+ps("0",7-t.length)+t);return r},n=d(function(){return"0.000"!==ys(8e-5,3)||"1"!==ys(.9,0)||"1.25"!==ys(1.255,2)||"1000000000000000128"!==ys(0xde0b6b3a7640080,0)})||!d(function(){ys({})});function Es(e){this.aegisPlugin=!0,this.name="",this.instances=[],this.inited=!1,e.$walk=this.walk.bind(this),e.$getConfig=this.getConfig.bind(this),this.option=e,this.name=e.name}C({target:"Number",proto:!0,forced:n},{toFixed:function(e){var t,n,r,o=Tu(this),e=ct(e),i=[0,0,0,0,0,0],a="",u="0";if(e<0||20<e)throw new ls("Incorrect fraction digits");if(o!=o)return"NaN";if(o<=-1e21||1e21<=o)return ds(o);if(o<0&&(a="-",o=-o),1e-21<o)if(r=(t=function(){for(var e=0,t=o*vs(2,69,1);4096<=t;)e+=12,t/=4096;for(;2<=t;)e+=1,t/=2;return e}()-69)<0?o*vs(2,-t,1):o/vs(2,t,1),r*=4503599627370496,0<(t=52-t)){for(ms(i,0,r),n=e;7<=n;)ms(i,1e7,0),n-=7;for(ms(i,vs(10,n,1),0),n=t-1;23<=n;)bs(i,1<<23),n-=23;bs(i,1<<n),ms(i,1,1),bs(i,2),u=ws(i)}else ms(i,0,r),ms(i,1<<-t,0),u=ws(i)+ps("0",e);return 0<e?a+((r=u.length)<=e?"0."+ps("0",e-r)+u:gs(u,0,r-e)+"."+gs(u,r-e)):a+u}});var Rs=function(t){var n;return t.payload?(n={},Object.keys(t).forEach(function(e){"payload"!==e&&(n[e]=t[e])}),n):t},Ss=function(t){return"string"!=typeof t||!t||ci.some(function(e){return-1<t.indexOf(e)})||si.some(function(e){return-1<t.indexOf(e)})},Os=function(){return"undefined"!=typeof window.performance&&"function"==typeof performance.getEntriesByType&&"function"==typeof performance.now},Ts=new Iu({name:"reportAssetSpeed"}),a=Ts=new Iu({name:"reportAssetSpeed",collectCur:0,collectEntryType:"resource",ASSETS_INITIATOR_TYPE:["img","css","script","link","audio","video"],onNewAegis:function(e){var t=this;Os()&&(this.collectSuccessLog(e),this.collectFailLog(e),performance.onresourcetimingbufferfull=function(){"function"==typeof performance.clearResourceTimings&&(t.collectCur=0,performance.clearResourceTimings())})},publish:function(t,n){this.$walk(function(e){e===n&&e.speedLogPipeline(t)})},publishMany:function(e,t){for(var n=t.config,r=0,o=e.length;r<o;r++){var i=e[r];-1===this.ASSETS_INITIATOR_TYPE.indexOf(i.initiatorType)||tu(i.name,n.hostUrl)||this.publish(this.generateLog(i,n),t)}},collectSuccessLog:function(n){var e,t,r=this;"function"==typeof window.PerformanceObserver?(this.publishMany(performance.getEntriesByType(this.collectEntryType),n),(e=new window.PerformanceObserver(function(e){r.publishMany(e.getEntries(),n)})).observe({entryTypes:[this.collectEntryType]}),n.lifeCycle.on("destroy",function(){0===Ts.countInstance()&&e.disconnect()})):(t=setInterval(function(){var e=performance.getEntriesByType(r.collectEntryType),t=e.slice(r.collectCur);r.collectCur=e.length,r.publishMany(t,n)},3e3),n.lifeCycle.on("destroy",function(){0===Ts.countInstance()&&clearInterval(t)}))},collectFailLog:function(r){var o=this,i=r.config,e=function(e){var t,n;e&&(e=e.target||e.srcElement,!(t=(null==e?void 0:e.src)||(null==e?void 0:e.href))||"string"!=typeof t||-1<window.location.href.indexOf(t)||(e="function"==typeof(null==(e=i.api)?void 0:e.resourceTypeHandler)?null==(e=i.api)?void 0:e.resourceTypeHandler(t):"",n=performance.getEntriesByType(o.collectEntryType).find(function(e){return e.name===t}),tu(t,i.hostUrl))||(n={url:Da(t),status:400,duration:Number(((null==n?void 0:n.duration)||0).toFixed(2)),method:"get",type:e||"static",isHttps:Ha(t),urlQuery:qa(t,!0),nextHopProtocol:"",domainLookup:0,connectTime:0},o.publish(n,r)))};window.document.addEventListener("error",e,!0),r.lifeCycle.on("destroy",function(){0===Ts.countInstance()&&window.document.removeEventListener("error",e,!0)})},generateLog:function(e,t){var t="function"==typeof(null==(n=t.api)?void 0:n.resourceTypeHandler)?null==(n=t.api)?void 0:n.resourceTypeHandler(e.name):"",n=e.transferSize;return{url:Da(e.name),method:"get",duration:Number(e.duration.toFixed(2)),status:200,type:t||"static",isHttps:Ha(e.name),nextHopProtocol:e.nextHopProtocol||"",urlQuery:qa(e.name,!0),domainLookup:Fa(e.domainLookupEnd-e.domainLookupStart),connectTime:Fa(e.connectEnd-e.connectStart),transferSize:0<n?n:-1}},collectNotReportedLog:function(e){var t,n;Os()&&(t=(n=performance.getEntriesByType(this.collectEntryType)).length,"function"!=typeof window.PerformanceObserver)&&this.collectCur!==t&&(n=n.slice(this.collectCur),this.collectCur=t,this.publishMany(n,e,!0))},destroy:function(){this.option.publish=function(){}}}),xs=Zt.findIndex,As=!0;"findIndex"in[]&&Array(1).findIndex(function(){As=!1}),C({target:"Array",proto:!0,forced:As},{findIndex:function(e){return xs(this,e,1<arguments.length?arguments[1]:undefined)}}),e("findIndex");var Ps,Is,Ls=TypeError,ks=function(e){if(Xt(e))return e;throw new Ls(le(e)+" is not a constructor")},Cs=s("species"),n=/(?:ipad|iphone|ipod).*applewebkit/i.test(oe),e=x.setImmediate,js=x.clearImmediate,Us=x.process,Ns=x.Dispatch,Ms=x.Function,_s=x.MessageChannel,Bs=x.String,Fs=0,qs={},Ds=(d(function(){Ps=x.location}),function(e){var t;L(qs,e)&&(t=qs[e],delete qs[e],t())}),Hs=function(e){return function(){Ds(e)}},Ws=function(e){Ds(e.data)},p=function(e){x.postMessage(Bs(e),Ps.protocol+"//"+Ps.host)},_s=(e&&js||(e=function(e){cu(arguments.length,1);var t=R(e)?e:Ms(e),n=m(arguments,1);return qs[++Fs]=function(){_o(t,undefined,n)},Is(Fs),Fs},js=function(e){delete qs[e]},iu?Is=function(e){Us.nextTick(Hs(e))}:Ns&&Ns.now?Is=function(e){Ns.now(Hs(e))}:_s&&!n?(Gs=(_s=new _s).port2,_s.port1.onmessage=Ws,Is=Ut(Gs.postMessage,Gs)):x.addEventListener&&R(x.postMessage)&&!x.importScripts&&Ps&&"file:"!==Ps.protocol&&!d(p)?(Is=p,x.addEventListener("message",Ws,!1)):Is="onreadystatechange"in Ne("script")?function(e){nn.appendChild(Ne("script")).onreadystatechange=function(){nn.removeChild(this),Ds(e)}}:function(e){setTimeout(Hs(e),0)}),{set:e,clear:js}),Vs=Object.getOwnPropertyDescriptor,Gs=function(e){var t;return A?(t=Vs(x,e))&&t.value:x[e]},p=function(){this.head=null,this.tail=null};p.prototype={add:function(e){var e={item:e,next:null},t=this.tail;t?t.next=e:this.head=e,this.tail=e},get:function(){var e=this.head;if(e)return null===(this.head=e.next)&&(this.tail=null),e.item}};var $s,zs,Xs,Ys,Js,Ks,Qs=p,Ws=/ipad|iphone|ipod/i.test(oe)&&"undefined"!=typeof Pebble,e=/web0s(?!.*chrome)/i.test(oe),Zs=_s.set,js=x.MutationObserver||x.WebKitMutationObserver,p=x.document,ec=x.process,w=x.Promise,E=Gs("queueMicrotask"),tc=(E||(Ys=new Qs,Js=function(){var e,t;for(iu&&(e=ec.domain)&&e.exit();t=Ys.get();)try{t()}catch(N){throw Ys.head&&Ks(),N}e&&e.enter()},Ks=n||iu||e||!js||!p?!Ws&&w&&w.resolve?((n=w.resolve(undefined)).constructor=w,Xs=Ut(n.then,n),function(){Xs(Js)}):iu?function(){ec.nextTick(Js)}:(Zs=Ut(Zs,x),function(){Zs(Js)}):($s=!0,zs=p.createTextNode(""),new js(Js).observe(zs,{characterData:!0}),function(){zs.data=$s=!$s}),E=function(e){Ys.head||Ks(),Ys.add(e)}),E),nc=function(e){try{return{error:!1,value:e()}}catch(N){return{error:!0,value:N}}},rc=x.Promise;rc&&rc.prototype;var oc,ic,ac,uc=s("species"),sc=!1,cc=R(x.PromiseRejectionEvent),e={CONSTRUCTOR:kt("Promise",function(){var e=et(rc),t=e!==String(rc);if(!t&&66===ie)return!0;if(!ie||ie<51||!/native code/.test(e)){var e=new rc(function(e){e(1)}),n=function(e){e(function(){},function(){})};if((e.constructor={})[uc]=n,!(sc=e.then(function(){})instanceof n))return!0}return!(t||"BROWSER"!=ou&&"DENO"!=ou||cc)}),REJECTION_EVENT:cc,SUBCLASSING:sc},fc=TypeError,lc=function(e){var n,r;this.promise=new e(function(e,t){if(n!==undefined||r!==undefined)throw new fc("Bad Promise constructor");n=e,r=t}),this.resolve=he(n),this.reject=he(r)},dc={f:function(e){return new lc(e)}},hc=_s.set,Ws=e.CONSTRUCTOR,pc=e.REJECTION_EVENT,w=e.SUBCLASSING,gc=k.getterFor("Promise"),yc=k.set,n=rc&&rc.prototype,vc=rc,mc=n,bc=x.TypeError,wc=x.document,Ec=x.process,Rc=dc.f,Sc=Rc,Oc=!!(wc&&wc.createEvent&&x.dispatchEvent),Tc=function(e){var t;return!(!I(e)||!R(t=e.then))&&t},xc=function(e,t){var n,r,o,i=t.value,a=1===t.state,u=a?e.ok:e.fail,s=e.resolve,c=e.reject,f=e.domain;try{u?(a||(2===t.rejection&&kc(t),t.rejection=1),!0===u?n=i:(f&&f.enter(),n=u(i),f&&(f.exit(),o=!0)),n===e.promise?c(new bc("Promise-chain cycle")):(r=Tc(n))?P(r,n,s,c):s(n)):c(i)}catch(N){f&&!o&&f.exit(),c(N)}},Ac=function(n,r){n.notified||(n.notified=!0,tc(function(){for(var e,t=n.reactions;e=t.get();)xc(e,n);n.notified=!1,r&&!n.rejection&&Ic(n)}))},Pc=function(e,t,n){var r;Oc?((r=wc.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),x.dispatchEvent(r)):r={promise:t,reason:n},!pc&&(t=x["on"+e])?t(r):"unhandledrejection"===e&&function(e,t){try{1===arguments.length?console.error(e):console.error(e,t)}catch(N){}}("Unhandled promise rejection",n)},Ic=function(r){P(hc,x,function(){var e,t=r.facade,n=r.value;if(Lc(r)&&(e=nc(function(){iu?Ec.emit("unhandledRejection",n,t):Pc("unhandledrejection",t,n)}),r.rejection=iu||Lc(r)?2:1,e.error))throw e.value})},Lc=function(e){return 1!==e.rejection&&!e.parent},kc=function(t){P(hc,x,function(){var e=t.facade;iu?Ec.emit("rejectionHandled",e):Pc("rejectionhandled",e,t.value)})},Cc=function(t,n,r){return function(e){t(n,e,r)}},jc=function(e,t,n){e.done||(e.done=!0,(e=n?n:e).value=t,e.state=2,Ac(e,!0))},Uc=function(t,n,e){if(!t.done){t.done=!0,e&&(t=e);try{if(t.facade===n)throw new bc("Promise can't be resolved itself");var r=Tc(n);r?tc(function(){var e={done:!1};try{P(r,n,Cc(Uc,e,t),Cc(jc,e,t))}catch(N){jc(e,N,t)}}):(t.value=n,t.state=1,Ac(t,!1))}catch(N){jc({done:!1},N,t)}}};if(Ws&&(mc=(vc=function(e){Rr(this,mc),he(e),P(oc,this);var t=gc(this);try{e(Cc(Uc,t),Cc(jc,t))}catch(N){jc(t,N)}}).prototype,(oc=function(e){yc(this,{type:"Promise",done:!1,notified:!1,parent:!1,reactions:new Qs,rejection:!1,state:0,value:null})}).prototype=g(mc,"then",function(e,t){var n,r,o=gc(this),i=Rc((n=vc,(r=S(this).constructor)===undefined||K(r=S(r)[Cs])?n:ks(r)));return o.parent=!0,i.ok=!R(e)||e,i.fail=R(t)&&t,i.domain=iu?Ec.domain:undefined,0===o.state?o.reactions.add(i):tc(function(){xc(i,o)}),i.promise}),ic=function(){var e=new oc,t=gc(e);this.promise=e,this.resolve=Cc(Uc,t),this.reject=Cc(jc,t)},dc.f=Rc=function(e){return e===vc||void 0===e?new ic:Sc(e)},R(rc))&&n!==Object.prototype){ac=n.then,w||g(n,"then",function(e,t){var n=this;return new vc(function(e,t){P(ac,n,e,t)}).then(e,t)},{unsafe:!0});try{delete n.constructor}catch(N){}Cn&&Cn(n,mc)}C({global:!0,constructor:!0,wrap:!0,forced:Ws},{Promise:vc}),xn(vc,"Promise",!1),xi("Promise");var Nc,Mc,_c,p=e.CONSTRUCTOR||!Wn(function(e){rc.all(e).then(undefined,function(){})}),js=(C({target:"Promise",stat:!0,forced:p},{all:function(e){var u=this,t=dc.f(u),s=t.resolve,c=t.reject,n=nc(function(){var r=he(u.resolve),o=[],i=0,a=1;wr(e,function(e){var t=i++,n=!1;a++,P(r,u,e).then(function(e){n||(n=!0,o[t]=e,--a)||s(o)},c)}),--a||s(o)});return n.error&&c(n.value),t.promise}}),e.CONSTRUCTOR),E=rc&&rc.prototype,w=(C({target:"Promise",proto:!0,forced:js,real:!0},{"catch":function(e){return this.then(undefined,e)}}),R(rc)&&(_s=ne("Promise").prototype["catch"],E["catch"]!==_s)&&g(E,"catch",_s,{unsafe:!0}),C({target:"Promise",stat:!0,forced:p},{race:function(e){var n=this,r=dc.f(n),o=r.reject,t=nc(function(){var t=he(n.resolve);wr(e,function(e){P(t,n,e).then(r.resolve,o)})});return t.error&&o(t.value),r.promise}}),C({target:"Promise",stat:!0,forced:e.CONSTRUCTOR},{reject:function(e){var t=dc.f(this);return(0,t.reject)(e),t.promise}}),e.CONSTRUCTOR),Bc=(ne("Promise"),C({target:"Promise",stat:!0,forced:w},{resolve:function(e){return e=e,S(t=this),I(e)&&e.constructor===t?e:((0,(t=dc.f(t)).resolve)(e),t.promise);var t}}),window.navigator.userAgent.toLowerCase()),T={},Fc=function(e){return-1!==Bc.indexOf(e)},qc=(T.macos=function(){return Fc("mac")},T.ios=function(){return T.iphone()||T.ipod()||T.ipad()},T.iphone=function(){return!T.windows()&&Fc("iphone")},T.ipod=function(){return Fc("ipod")},T.ipad=function(){var e="MacIntel"===navigator.platform&&1<navigator.maxTouchPoints;return Fc("ipad")||e},T.android=function(){return!T.windows()&&Fc("android")},T.androidPhone=function(){return T.android()&&Fc("mobile")},T.androidTablet=function(){return T.android()&&!Fc("mobile")},T.blackberry=function(){return Fc("blackberry")||Fc("bb10")},T.blackberryPhone=function(){return T.blackberry()&&!Fc("tablet")},T.blackberryTablet=function(){return T.blackberry()&&Fc("tablet")},T.windows=function(){return Fc("windows")},T.windowsPhone=function(){return T.windows()&&Fc("phone")},T.windowsTablet=function(){return T.windows()&&Fc("touch")&&!T.windowsPhone()},T.fxos=function(){return(Fc("(mobile")||Fc("(tablet"))&&Fc(" rv:")},T.fxosPhone=function(){return T.fxos()&&Fc("mobile")},T.fxosTablet=function(){return T.fxos()&&Fc("tablet")},T.meego=function(){return Fc("meego")},T.cordova=function(){return window.cordova&&"file:"===location.protocol},T.nodeWebkit=function(){return"object"===vo(window.process)},T.mobile=function(){return T.androidPhone()||T.iphone()||T.ipod()||T.windowsPhone()||T.blackberryPhone()||T.fxosPhone()||T.meego()},T.tablet=function(){return T.ipad()||T.androidTablet()||T.blackberryTablet()||T.windowsTablet()||T.fxosTablet()},T.desktop=function(){return!T.tablet()&&!T.mobile()},T.isIE=function(){return"ActiveXObject"in window},s("iterator")),n=!d(function(){var e=new URL("b?a=1&b=2&c=3","https://a"),n=e.searchParams,t=new URLSearchParams("a=1&a=2&b=3"),r="";return e.pathname="c%20d",n.forEach(function(e,t){n["delete"]("b"),r+=t+e}),t["delete"]("a",2),t["delete"]("b",undefined),!n.size&&!A||!n.sort||"https://a/c%20d?a=1&c=3"!==e.href||"3"!==n.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!n[qc]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==r||"x"!==new URL("https://x",undefined).host}),Dc=Array,Hc=function(e){var t=we(e),e=Xt(this),n=arguments.length,r=1<n?arguments[1]:undefined,o=r!==undefined;o&&(r=Ut(r,2<n?arguments[2]:undefined));var i,a,u,s,c,f,n=hr(t),l=0;if(!n||this===Dc&&lr(n))for(i=gt(t),a=e?new this(i):Dc(i);l<i;l++)f=o?r(t[l],l):t[l],zr(a,l,f);else for(a=e?new this:[],c=(s=gr(t,n)).next;!(u=P(c,s)).done;l++)f=o?ri(s,r,[u.value,l],!0):u.value,zr(a,l,f);return a.length=l,a},Wc=/[^\0-\u007E]/,Vc=/[.\u3002\uFF0E\uFF61]/g,Gc="Overflow: input needs wider integers to process",$c=RangeError,zc=h(Vc.exec),Xc=Math.floor,Yc=String.fromCharCode,Jc=h("".charCodeAt),Kc=h([].join),Qc=h([].push),Zc=h("".replace),ef=h("".split),tf=h("".toLowerCase),nf=function(e){return e+22+75*(e<26)},rf=function(e){var t,n=[],r=(e=function(e){for(var t=[],n=0,r=e.length;n<r;){var o,i=Jc(e,n++);55296<=i&&i<=56319&&n<r?56320==(64512&(o=Jc(e,n++)))?Qc(t,((1023&i)<<10)+(1023&o)+65536):(Qc(t,i),n--):Qc(t,i)}return t}(e)).length,o=128,i=0,a=72;for(f=0;f<e.length;f++)(t=e[f])<128&&Qc(n,Yc(t));var u=n.length,s=u;for(u&&Qc(n,"-");s<r;){for(var c=2147483647,f=0;f<e.length;f++)(t=e[f])>=o&&t<c&&(c=t);var l=s+1;if(c-o>Xc((2147483647-i)/l))throw new $c(Gc);for(i+=(c-o)*l,o=c,f=0;f<e.length;f++){if((t=e[f])<o&&2147483647<++i)throw new $c(Gc);if(t===o){for(var d=i,h=36;;){var p=h<=a?1:a+26<=h?26:h-a;if(d<p)break;var g=d-p,y=36-p;Qc(n,Yc(nf(p+g%y))),d=Xc(g/y),h+=36}Qc(n,Yc(nf(d))),a=function(e,t,n){var r=0;for(e=n?Xc(e/700):e>>1,e+=Xc(e/t);455<e;)e=Xc(e/35),r+=36;return Xc(r+36*e/(e+38))}(i,l,s===u),i=0,s++}}i++,o++}return Kc(n,"")},of=RangeError,af=String.fromCharCode,Ws=String.fromCodePoint,uf=h([].join),js=!!Ws&&1!==Ws.length,sf=(C({target:"String",stat:!0,arity:1,forced:js},{fromCodePoint:function(e){for(var t,n=[],r=arguments.length,o=0;o<r;){if(t=+arguments[o++],dt(t,1114111)!==t)throw new of(t+" is not a valid code point");n[o]=t<65536?af(t):af(55296+((t-=65536)>>10),t%1024+56320)}return uf(n,"")}}),Math.floor),cf=function(e,t){var n=e.length;if(n<8)for(var r,o,i=1;i<n;){for(r=e[o=i];o&&0<t(e[o-1],r);)e[o]=e[--o];o!==i++&&(e[o]=r)}else for(var a=sf(n/2),u=cf(m(e,0,a),t),s=cf(m(e,a),t),c=u.length,f=s.length,l=0,d=0;l<c||d<f;)e[l+d]=l<c&&d<f?t(u[l],s[d])<=0?u[l++]:s[d++]:l<c?u[l++]:s[d++];return e},ff=cf,E=s("iterator"),lf=k.set,df=k.getterFor("URLSearchParams"),hf=k.getterFor("URLSearchParamsIterator"),pf=Gs("fetch"),gf=Gs("Request"),yf=Gs("Headers"),vf=gf&&gf.prototype,_s=yf&&yf.prototype,mf=x.TypeError,bf=x.encodeURIComponent,wf=String.fromCharCode,Ef=ne("String","fromCodePoint"),Rf=parseInt,Sf=h("".charAt),Of=h([].join),Tf=h([].push),xf=h("".replace),Af=h([].shift),Pf=h([].splice),If=h("".split),Lf=h("".slice),kf=h(/./.exec),Cf=/\+/g,jf=/^[0-9a-f]+$/i,Uf=function(e,t){e=Lf(e,t,t+2);return kf(jf,e)?Rf(e,16):NaN},Nf=function(e){for(var t=(e=xf(e,Cf," ")).length,n="",r=0;r<t;){var o=Sf(e,r);if("%"===o){if("%"===Sf(e,r+1)||t<r+3){n+="%",r++;continue}var i=Uf(e,r+1);if(i!=i){n+=o,r++;continue}r+=2;var a=function(e){for(var t=0,n=128;0<n&&0!=(e&n);n>>=1)t++;return t}(i);if(0===a)o=wf(i);else{if(1===a||4<a){n+="�",r++;continue}for(var u=[i],s=1;s<a&&!(++r+3>t||"%"!==Sf(e,r));){var c=Uf(e,r+1);if(c!=c){r+=3;break}if(191<c||c<128)break;Tf(u,c),r+=2,s++}if(u.length!==a){n+="�";continue}i=function(e){var t=null;switch(e.length){case 1:t=e[0];break;case 2:t=(31&e[0])<<6|63&e[1];break;case 3:t=(15&e[0])<<12|(63&e[1])<<6|63&e[2];break;case 4:t=(7&e[0])<<18|(63&e[1])<<12|(63&e[2])<<6|63&e[3]}return 1114111<t?null:t}(u);null===i?n+="�":o=Ef(i)}}n+=o,r++}return n},Mf=/[!'()~]|%20/g,_f={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},Bf=function(e){return _f[e]},Ff=function(e){return xf(bf(e),Mf,Bf)},qf=In(function(e,t){lf(this,{type:"URLSearchParamsIterator",target:df(e).entries,index:0,kind:t})},"URLSearchParams",function(){var e=hf(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,Fn(undefined,!0);var r=t[n];switch(e.kind){case"keys":return Fn(r.key,!1);case"values":return Fn(r.value,!1)}return Fn([r.key,r.value],!1)},!0),Df=function(e){this.entries=[],this.url=null,e!==undefined&&(I(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===Sf(e,0)?Lf(e,1):e:O(e)))},p=(Df.prototype={type:"URLSearchParams",bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,r,o,i,a,u=this.entries,s=hr(e);if(s)for(n=(t=gr(e,s)).next;!(r=P(n,t)).done;){if(o=(r=gr(S(r.value))).next,(i=P(o,r)).done||(a=P(o,r)).done||!P(o,r).done)throw new mf("Expected sequence with length 2");Tf(u,{key:O(i.value),value:O(a.value)})}else for(var c in e)L(e,c)&&Tf(u,{key:c,value:O(e[c])})},parseQuery:function(e){if(e)for(var t,n=this.entries,r=If(e,"&"),o=0;o<r.length;)(t=r[o++]).length&&(t=If(t,"="),Tf(n,{key:Nf(Af(t)),value:Nf(Of(t,"="))}))},serialize:function(){for(var e,t=this.entries,n=[],r=0;r<t.length;)e=t[r++],Tf(n,Ff(e.key)+"="+Ff(e.value));return Of(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}},function(){Rr(this,Hf);var e=0<arguments.length?arguments[0]:undefined,e=lf(this,new Df(e));A||(this.size=e.entries.length)}),Hf=p.prototype;Mr(Hf,{append:function(e,t){var n=df(this);cu(arguments.length,2),Tf(n.entries,{key:O(e),value:O(t)}),A||this.length++,n.updateURL()},"delete":function(e){for(var t=df(this),n=cu(arguments.length,1),r=t.entries,o=O(e),e=n<2?undefined:arguments[1],i=e===undefined?e:O(e),a=0;a<r.length;){var u=r[a];if(u.key!==o||i!==undefined&&u.value!==i)a++;else if(Pf(r,a,1),i!==undefined)break}A||(this.size=r.length),t.updateURL()},get:function(e){var t=df(this).entries;cu(arguments.length,1);for(var n=O(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){var t=df(this).entries;cu(arguments.length,1);for(var n=O(e),r=[],o=0;o<t.length;o++)t[o].key===n&&Tf(r,t[o].value);return r},has:function(e){for(var t=df(this).entries,n=cu(arguments.length,1),r=O(e),e=n<2?undefined:arguments[1],o=e===undefined?e:O(e),i=0;i<t.length;){var a=t[i++];if(a.key===r&&(o===undefined||a.value===o))return!0}return!1},set:function(e,t){var n=df(this);cu(arguments.length,1);for(var r,o=n.entries,i=!1,a=O(e),u=O(t),s=0;s<o.length;s++)(r=o[s]).key===a&&(i?Pf(o,s--,1):(i=!0,r.value=u));i||Tf(o,{key:a,value:u}),A||(this.size=o.length),n.updateURL()},sort:function(){var e=df(this);ff(e.entries,function(e,t){return e.key>t.key?1:-1}),e.updateURL()},forEach:function(e){for(var t,n=df(this).entries,r=Ut(e,1<arguments.length?arguments[1]:undefined),o=0;o<n.length;)r((t=n[o++]).value,t.key,this)},keys:function(){return new qf(this,"keys")},values:function(){return new qf(this,"values")},entries:function(){return new qf(this,"entries")}},{enumerable:!0}),g(Hf,E,Hf.entries,{name:"entries"}),g(Hf,"toString",function(){return df(this).serialize()},{enumerable:!0}),A&&c(Hf,"size",{get:function(){return df(this).entries.length},configurable:!0,enumerable:!0}),xn(p,"URLSearchParams"),C({global:!0,constructor:!0,forced:!n},{URLSearchParams:p}),!n&&R(yf)&&(Nc=h(_s.has),Mc=h(_s.set),_c=function(e){if(I(e)){var t,n=e.body;if("URLSearchParams"===Ft(n))return t=e.headers?new yf(e.headers):new yf,Nc(t,"content-type")||Mc(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),cn(e,{body:W(0,O(n)),headers:W(0,t)})}return e},R(pf)&&C({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return pf(e,1<arguments.length?_c(arguments[1]):{})}}),R(gf))&&((vf.constructor=e=function(e){return Rr(this,vf),new gf(e,1<arguments.length?_c(arguments[1]):{})}).prototype=vf,C({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:e}));var Wf,w={URLSearchParams:p,getState:df},Vf=o.codeAt,Gf=k.set,$f=k.getterFor("URL"),zf=w.URLSearchParams,Xf=w.getState,Ws=x.URL,Yf=x.TypeError,Jf=x.parseInt,Kf=Math.floor,Qf=Math.pow,Zf=h("".charAt),el=h(/./.exec),tl=h([].join),nl=h(1..toString),rl=h([].pop),ol=h([].push),il=h("".replace),al=h([].shift),ul=h("".split),sl=h("".slice),cl=h("".toLowerCase),fl=h([].unshift),ll=/[a-z]/i,dl=/[\d+-.a-z]/i,hl=/\d/,pl=/^0x/i,gl=/^[0-7]+$/,yl=/^\d+$/,vl=/^[\da-f]+$/i,ml=/[\0\t\n\r #%/:<>?@[\\\]^|]/,bl=/[\0\t\n\r #/:<>?@[\\\]^|]/,wl=/^[\u0000-\u0020]+/,El=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,Rl=/[\t\n\r]/g,Sl=function(e){var t,n,r,o;if("number"==typeof e){for(t=[],n=0;n<4;n++)fl(t,e%256),e=Kf(e/256);return tl(t,".")}if("object"!=typeof e)return e;for(t="",r=function(e){for(var t=null,n=1,r=null,o=0,i=0;i<8;i++)0!==e[i]?(n<o&&(t=r,n=o),r=null,o=0):(null===r&&(r=i),++o);return n<o?r:t}(e),n=0;n<8;n++)o&&0===e[n]||(o=o&&!1,r===n?(t+=n?":":"::",o=!0):(t+=nl(e[n],16),n<7&&(t+=":")));return"["+t+"]"},Ol={},Tl=go({},Ol,{" ":1,'"':1,"<":1,">":1,"`":1}),xl=go({},Tl,{"#":1,"?":1,"{":1,"}":1}),Al=go({},xl,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Pl=function(e,t){var n=Vf(e,0);return 32<n&&n<127&&!L(t,e)?e:encodeURIComponent(e)},Il={ftp:21,file:null,http:80,https:443,ws:80,wss:443},Ll=function(e,t){return 2===e.length&&el(ll,Zf(e,0))&&(":"===(e=Zf(e,1))||!t&&"|"===e)},kl=function(e){return 1<e.length&&Ll(sl(e,0,2))&&(2===e.length||"/"===(e=Zf(e,2))||"\\"===e||"?"===e||"#"===e)},Cl={},jl={},Ul={},Nl={},Ml={},_l={},Bl={},Fl={},ql={},Dl={},Hl={},Wl={},Vl={},Gl={},$l={},zl={},Xl={},Yl={},Jl={},Kl={},Ql={},Zl=function(e,t,n){var r,o,e=O(e);if(t){if(o=this.parse(e))throw new Yf(o);this.searchParams=null}else{if(n!==undefined&&(r=new Zl(n,!0)),o=this.parse(e,null,r))throw new Yf(o);(t=Xf(new zf)).bindURL(this),this.searchParams=t}},ed=(Zl.prototype={type:"URL",parse:function(e,t,n){var r,o,i,a,u=this,s=t||Cl,c=0,f="",l=!1,d=!1,h=!1;for(e=O(e),t||(u.scheme="",u.username="",u.password="",u.host=null,u.port=null,u.path=[],u.query=null,u.fragment=null,u.cannotBeABaseURL=!1,e=il(e,wl,""),e=il(e,El,"$1")),e=il(e,Rl,""),r=Hc(e);c<=r.length;){switch(o=r[c],s){case Cl:if(!o||!el(ll,o)){if(t)return"Invalid scheme";s=Ul;continue}f+=cl(o),s=jl;break;case jl:if(o&&(el(dl,o)||"+"===o||"-"===o||"."===o))f+=cl(o);else{if(":"!==o){if(t)return"Invalid scheme";f="",s=Ul,c=0;continue}if(t&&(u.isSpecial()!==L(Il,f)||"file"===f&&(u.includesCredentials()||null!==u.port)||"file"===u.scheme&&!u.host))return;if(u.scheme=f,t)return void(u.isSpecial()&&Il[u.scheme]===u.port&&(u.port=null));f="","file"===u.scheme?s=Gl:u.isSpecial()&&n&&n.scheme===u.scheme?s=Nl:u.isSpecial()?s=Fl:"/"===r[c+1]?(s=Ml,c++):(u.cannotBeABaseURL=!0,ol(u.path,""),s=Jl)}break;case Ul:if(!n||n.cannotBeABaseURL&&"#"!==o)return"Invalid scheme";if(n.cannotBeABaseURL&&"#"===o){u.scheme=n.scheme,u.path=m(n.path),u.query=n.query,u.fragment="",u.cannotBeABaseURL=!0,s=Ql;break}s="file"===n.scheme?Gl:_l;continue;case Nl:if("/"!==o||"/"!==r[c+1]){s=_l;continue}s=ql,c++;break;case Ml:if("/"===o){s=Dl;break}s=Yl;continue;case _l:if(u.scheme=n.scheme,o===Wf)u.username=n.username,u.password=n.password,u.host=n.host,u.port=n.port,u.path=m(n.path),u.query=n.query;else if("/"===o||"\\"===o&&u.isSpecial())s=Bl;else if("?"===o)u.username=n.username,u.password=n.password,u.host=n.host,u.port=n.port,u.path=m(n.path),u.query="",s=Kl;else{if("#"!==o){u.username=n.username,u.password=n.password,u.host=n.host,u.port=n.port,u.path=m(n.path),u.path.length--,s=Yl;continue}u.username=n.username,u.password=n.password,u.host=n.host,u.port=n.port,u.path=m(n.path),u.query=n.query,u.fragment="",s=Ql}break;case Bl:if(!u.isSpecial()||"/"!==o&&"\\"!==o){if("/"!==o){u.username=n.username,u.password=n.password,u.host=n.host,u.port=n.port,s=Yl;continue}s=Dl}else s=ql;break;case Fl:if(s=ql,"/"!==o||"/"!==Zf(f,c+1))continue;c++;break;case ql:if("/"===o||"\\"===o)break;s=Dl;continue;case Dl:if("@"===o){l&&(f="%40"+f);for(var l=!0,p=Hc(f),g=0;g<p.length;g++){var y=p[g];":"!==y||h?(y=Pl(y,Al),h?u.password+=y:u.username+=y):h=!0}f=""}else if(o===Wf||"/"===o||"?"===o||"#"===o||"\\"===o&&u.isSpecial()){if(l&&""===f)return"Invalid authority";c-=Hc(f).length+1,f="",s=Hl}else f+=o;break;case Hl:case Wl:if(t&&"file"===u.scheme){s=zl;continue}if(":"!==o||d){if(o===Wf||"/"===o||"?"===o||"#"===o||"\\"===o&&u.isSpecial()){if(u.isSpecial()&&""===f)return"Invalid host";if(t&&""===f&&(u.includesCredentials()||null!==u.port))return;if(i=u.parseHost(f))return i;if(f="",s=Xl,t)return;continue}"["===o?d=!0:"]"===o&&(d=!1),f+=o}else{if(""===f)return"Invalid host";if(i=u.parseHost(f))return i;if(f="",s=Vl,t===Wl)return}break;case Vl:if(!el(hl,o)){if(o===Wf||"/"===o||"?"===o||"#"===o||"\\"===o&&u.isSpecial()||t){if(""!==f){var v=Jf(f,10);if(65535<v)return"Invalid port";u.port=u.isSpecial()&&v===Il[u.scheme]?null:v,f=""}if(t)return;s=Xl;continue}return"Invalid port"}f+=o;break;case Gl:if(u.scheme="file","/"===o||"\\"===o)s=$l;else{if(!n||"file"!==n.scheme){s=Yl;continue}switch(o){case Wf:u.host=n.host,u.path=m(n.path),u.query=n.query;break;case"?":u.host=n.host,u.path=m(n.path),u.query="",s=Kl;break;case"#":u.host=n.host,u.path=m(n.path),u.query=n.query,u.fragment="",s=Ql;break;default:kl(tl(m(r,c),""))||(u.host=n.host,u.path=m(n.path),u.shortenPath()),s=Yl;continue}}break;case $l:if("/"===o||"\\"===o){s=zl;break}n&&"file"===n.scheme&&!kl(tl(m(r,c),""))&&(Ll(n.path[0],!0)?ol(u.path,n.path[0]):u.host=n.host),s=Yl;continue;case zl:if(o===Wf||"/"===o||"\\"===o||"?"===o||"#"===o){if(!t&&Ll(f))s=Yl;else{if(""===f){if(u.host="",t)return}else{if(i=u.parseHost(f))return i;if("localhost"===u.host&&(u.host=""),t)return;f=""}s=Xl}continue}f+=o;break;case Xl:if(u.isSpecial()){if(s=Yl,"/"!==o&&"\\"!==o)continue}else if(t||"?"!==o)if(t||"#"!==o){if(o!==Wf&&(s=Yl,"/"!==o))continue}else u.fragment="",s=Ql;else u.query="",s=Kl;break;case Yl:if(o===Wf||"/"===o||"\\"===o&&u.isSpecial()||!t&&("?"===o||"#"===o)){if(".."===(v=cl(f))||"%2e."===v||".%2e"===v||"%2e%2e"===v?(u.shortenPath(),"/"===o||"\\"===o&&u.isSpecial()||ol(u.path,"")):"."===(a=f)||"%2e"===cl(a)?"/"===o||"\\"===o&&u.isSpecial()||ol(u.path,""):("file"===u.scheme&&!u.path.length&&Ll(f)&&(u.host&&(u.host=""),f=Zf(f,0)+":"),ol(u.path,f)),f="","file"===u.scheme&&(o===Wf||"?"===o||"#"===o))for(;1<u.path.length&&""===u.path[0];)al(u.path);"?"===o?(u.query="",s=Kl):"#"===o&&(u.fragment="",s=Ql)}else f+=Pl(o,xl);break;case Jl:"?"===o?(u.query="",s=Kl):"#"===o?(u.fragment="",s=Ql):o!==Wf&&(u.path[0]+=Pl(o,Ol));break;case Kl:t||"#"!==o?o!==Wf&&("'"===o&&u.isSpecial()?u.query+="%27":u.query+="#"===o?"%23":Pl(o,Ol)):(u.fragment="",s=Ql);break;case Ql:o!==Wf&&(u.fragment+=Pl(o,Tl))}c++}},parseHost:function(o){var e,t,n;if("["===Zf(o,0))return"]"===Zf(o,o.length-1)&&(e=function(e){var t,n,r,o,i,a,u,s=[0,0,0,0,0,0,0,0],c=0,f=null,l=0,d=function(){return Zf(e,l)};if(":"===d()){if(":"!==Zf(e,1))return;l+=2,f=++c}for(;d();){if(8===c)return;if(":"!==d()){for(t=n=0;n<4&&el(vl,d());)t=16*t+Jf(d(),16),l++,n++;if("."===d()){if(0===n)return;if(l-=n,6<c)return;for(r=0;d();){if(o=null,0<r){if(!("."===d()&&r<4))return;l++}if(!el(hl,d()))return;for(;el(hl,d());){if(i=Jf(d(),10),null===o)o=i;else{if(0===o)return;o=10*o+i}if(255<o)return;l++}s[c]=256*s[c]+o,2!=++r&&4!==r||c++}if(4!==r)return;break}if(":"===d()){if(l++,!d())return}else if(d())return;s[c++]=t}else{if(null!==f)return;l++,f=++c}}if(null!==f)for(a=c-f,c=7;0!==c&&0<a;)u=s[c],s[c--]=s[f+a-1],s[f+--a]=u;else if(8!==c)return;return s}(sl(o,1,-1)))?void(this.host=e):"Invalid host";if(this.isSpecial())return o=function(){for(var e,t=[],n=ef(Zc(tf(o),Vc,"."),"."),r=0;r<n.length;r++)e=n[r],Qc(t,zc(Wc,e)?"xn--"+rf(e):e);return Kc(t,".")}(),el(ml,o)||null===(e=function(e){var t,n,r,o,i,a,u,s=ul(e,".");if(s.length&&""===s[s.length-1]&&s.length--,4<(t=s.length))return e;for(n=[],r=0;r<t;r++){if(""===(o=s[r]))return e;if(i=10,1<o.length&&"0"===Zf(o,0)&&(i=el(pl,o)?16:8,o=sl(o,8===i?1:2)),""===o)a=0;else{if(!el(10===i?yl:8===i?gl:vl,o))return e;a=Jf(o,i)}ol(n,a)}for(r=0;r<t;r++)if(a=n[r],r===t-1){if(a>=Qf(256,5-t))return null}else if(255<a)return null;for(u=rl(n),r=0;r<n.length;r++)u+=n[r]*Qf(256,3-r);return u}(o))?"Invalid host":void(this.host=e);if(el(bl,o))return"Invalid host";for(e="",t=Hc(o),n=0;n<t.length;n++)e+=Pl(t[n],Ol);this.host=e},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return L(Il,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"===this.scheme&&1===t&&Ll(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,n=e.username,r=e.password,o=e.host,i=e.port,a=e.path,u=e.query,s=e.fragment,c=t+":";return null!==o?(c+="//",e.includesCredentials()&&(c+=n+(r?":"+r:"")+"@"),c+=Sl(o),null!==i&&(c+=":"+i)):"file"===t&&(c+="//"),c+=e.cannotBeABaseURL?a[0]:a.length?"/"+tl(a,"/"):"",null!==u&&(c+="?"+u),null!==s&&(c+="#"+s),c},setHref:function(e){e=this.parse(e);if(e)throw new Yf(e);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"===e)try{return new ed(e.path[0]).origin}catch(N){return"null"}return"file"!==e&&this.isSpecial()?e+"://"+Sl(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(O(e)+":",Cl)},getUsername:function(){return this.username},setUsername:function(e){var t=Hc(O(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<t.length;n++)this.username+=Pl(t[n],Al)}},getPassword:function(){return this.password},setPassword:function(e){var t=Hc(O(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<t.length;n++)this.password+=Pl(t[n],Al)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?Sl(e):Sl(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,Hl)},getHostname:function(){var e=this.host;return null===e?"":Sl(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,Wl)},getPort:function(){var e=this.port;return null===e?"":O(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(""===(e=O(e))?this.port=null:this.parse(e,Vl))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+tl(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,Xl))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){""===(e=O(e))?this.query=null:("?"===Zf(e,0)&&(e=sl(e,1)),this.query="",this.parse(e,Kl)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){""!==(e=O(e))?("#"===Zf(e,0)&&(e=sl(e,1)),this.fragment="",this.parse(e,Ql)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}},function(e){var t=Rr(this,td),n=1<cu(arguments.length,1)?arguments[1]:undefined,e=Gf(t,new Zl(e,!1,n));A||(t.href=e.serialize(),t.origin=e.getOrigin(),t.protocol=e.getProtocol(),t.username=e.getUsername(),t.password=e.getPassword(),t.host=e.getHost(),t.hostname=e.getHostname(),t.port=e.getPort(),t.pathname=e.getPathname(),t.search=e.getSearch(),t.searchParams=e.getSearchParams(),t.hash=e.getHash())}),td=ed.prototype,js=function(e,t){return{get:function(){return $f(this)[e]()},set:t&&function(e){return $f(this)[t](e)},configurable:!0,enumerable:!0}};A&&(c(td,"href",js("serialize","setHref")),c(td,"origin",js("getOrigin")),c(td,"protocol",js("getProtocol","setProtocol")),c(td,"username",js("getUsername","setUsername")),c(td,"password",js("getPassword","setPassword")),c(td,"host",js("getHost","setHost")),c(td,"hostname",js("getHostname","setHostname")),c(td,"port",js("getPort","setPort")),c(td,"pathname",js("getPathname","setPathname")),c(td,"search",js("getSearch","setSearch")),c(td,"searchParams",js("getSearchParams")),c(td,"hash",js("getHash","setHash"))),g(td,"toJSON",function(){return $f(this).serialize()},{enumerable:!0}),g(td,"toString",function(){return $f(this).serialize()},{enumerable:!0}),Ws&&(Gs=Ws.createObjectURL,E=Ws.revokeObjectURL,Gs&&g(ed,"createObjectURL",Ut(Gs,Ws)),E)&&g(ed,"revokeObjectURL",Ut(E,Ws)),xn(ed,"URL"),C({global:!0,constructor:!0,forced:!n,sham:!A},{URL:ed}),C({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return P(URL.prototype.toString,this)}});var nd,rd,od,id,ad,ud,sd,cd=function(){return{host:new URL(location.href).host,pathname:location.pathname}},fd=!1,ld=[],dd=/^\/[^/]/,hd=!1,pd=[],gd=function(r,e,o){return null!=e&&e.length&&"object"===vo(r)?e.reduce(function(e,t){var n=window.Headers&&r instanceof Headers?r.get(t):r[t];return n?e+(""===e?"":"\n\n")+o+" header "+t+": "+n:e},""):""},yd=function(e,t){return e&&-1===["null","undefined"].indexOf(e)?t+": "+e:""},vd=function(e,t,n){void 0===t&&(t="");var r,o,e="function"==typeof(null==(o=e.api)?void 0:o.resourceTypeHandler)?null==(o=e.api)?void 0:o.resourceTypeHandler(n):"";return-1===li.indexOf(e)&&(r=void 0===t?"":t,o=(void 0===n?"":n).split("?")[0],e=Va.test(o)||Wa.some(function(e){return-1!==String(r).indexOf(e)})?"static":"fetch"),e},md=function(e,t){return e?y.AJAX_ERROR:t?y.RET_ERROR:y.API_RESPONSE},bd=function(e,t){if(null!=(e=e.api)&&e.usePerformanceTiming&&"string"==typeof t.url){e=null==(e=performance.getEntriesByName(t.url))?void 0:e.pop();if(e)return{url:t.url,isHttps:Ha(t.url),method:t.method,type:t.type,status:t.status,duration:Number(e.duration.toFixed(2)),nextHopProtocol:e.nextHopProtocol||"",domainLookup:Fa(e.domainLookupEnd-e.domainLookupStart),connectTime:Fa(e.connectEnd-e.connectStart)}}return{url:t.url,isHttps:Ha(t.url),method:t.method,type:t.type,status:t.status,duration:Number(t.duration.toFixed(2)),nextHopProtocol:"",domainLookup:Ua.number,connectTime:Ua.number}},_s=(new Iu({name:"reportApiSpeed"}),new Iu({name:"reportApiSpeed",override:!1,onNewAegis:function(e){var t,n;this.override||(this.override=!0,null!=(n=e.config.api)&&n.injectTraceHeader&&(this.traceRequestHeader=new es(n.injectTraceHeader,null!=(t=null==n?void 0:n.injectTraceIgnoreUrls)?t:[],null==n?void 0:n.injectTraceUrls,null==n?void 0:n.traceFlag)),this.overrideFetch(e.config,e),this.overrideXhr(e.config,e))},overrideFetch:function(v,m){var b=this,e=v.api,e={name:this.name,traceRequestHeader:null!=e&&e.injectTraceHeader?this.traceRequestHeader:null,then:function(l,d,h,p,g){var e,y;tu(h,v.hostUrl)||(e=l.headers?l.headers.get("content-type"):"","fetch"===(y=vd(v,e||"",h))?l.clone().text().then(function(n){var e,r=l.status<=0||400<=l.status,t=(null==(t=v.api)?void 0:t.reqHeaders)||[],o=gd(null==p?void 0:p.headers,t,"req"),t=(null==(t=v.api)?void 0:t.resHeaders)||[],i=gd(l.headers,t,"res"),a=ns(null==p?void 0:p.headers),t=$a(n,v.api,{url:h,ctx:l,payload:null==p?void 0:p.body}),u=t.code,s=t.isErr,t=null==(t=v.api)?void 0:t.apiDetail,c=t?za(null==p?void 0:p.body,null==(e=v.api)?void 0:e.reqParamHandler,{url:h}):"",f=t?za(n,null==(e=v.api)?void 0:e.resBodyHandler,{url:h,ctx:l}):"";setTimeout(function(){var e=bd(v,{url:h,duration:d,type:y,status:l.status||0,method:(null==p?void 0:p.method)||"get"}),t=[r?"FETCH_ERROR: "+n:"","fetch req url: "+h,"res status: "+(e.status||0),"res duration: "+e.duration+"ms","res startTime: "+g,o,i,"req method: "+(e.method||"GET"),"res retcode: "+u,yd(c,"req param"),yd(f,"res data")].filter(function(e){return e}).join("\n\n");e.payload=null==p?void 0:p.body,e.ret=u,e.isErr=+s,b.publishNormalLog({msg:t,level:md(r,s),code:u,trace:a},m),b.publishSpeed(e,m)},0)}):setTimeout(function(){var e=bd(v,{url:h,duration:d,type:y,status:l.status||0,method:(null==p?void 0:p.method)||"get"});e.type="static",e.urlQuery=qa(h,!0),b.publishSpeed(e,m)},0))},"catch":function(t,n,r,o,i){var a,e,u,s,c;throw tu(r,v.hostUrl)||(a=vd(v,"",r),e=(null==(e=v.api)?void 0:e.reqHeaders)||[],u=gd(null==o?void 0:o.headers,e,"req"),s=ns(null==o?void 0:o.headers),c=null!=(e=v.api)&&e.apiDetail?za(null==o?void 0:o.body,null==(e=v.api)?void 0:e.reqParamHandler,{url:r}):"",setTimeout(function(){var e=bd(v,{url:r,duration:n,type:a,status:0,method:(null==o?void 0:o.method)||"get"}),e=(b.publishSpeed(e,m),"AJAX_ERROR: "+t+"\n                          \nreq url: "+r+"\n                          \nres status: 0\n                          \nres duration: "+e.duration+"ms\n                          \nreq method: "+((null==o?void 0:o.method)||"get")+"\n                          \nreq param: "+c+"\n                          \nres startTime: "+i+"\n                          \n"+u);b.publishNormalLog({msg:e,level:y.AJAX_ERROR,code:-400,trace:s},m)},0)),t}},f=(this.hackFetchOptions=e,this.hackFetchOptions),l=null==(e=v.api)?void 0:e.ignoreHackReg;if(void 0===l&&(l=/\.flv(\?|$)/i),pd.find(function(e){return e.name===f.name}))throw new Error("name '"+f.name+"' is already in hackFetch option list");pd.push(f),!hd&&window.fetch&&(hd=!0,id=window.fetch,window.fetch=function(e,o){void 0===o&&(o={});var i="string"==typeof e?e:null==e?void 0:e.url;if(null!=(a=null==l?void 0:l.test)&&a.call(l,i))return id(i,o);dd.test(i)&&(i=""+location.origin+i);var t,n,r,a=(f||{}).traceRequestHeader;a&&(t=void 0===(t=(o||{}).headers)?{}:t,n=(r=cd()).host,r=(n=a.generate(i,t,{host:n,pathname:r.pathname})||{}).name,a=n.value)&&r&&(o.headers=Object.assign(t,((n={})[r]=a,n)));for(var u=0;u<pd.length;u++){var s=pd[u];try{"function"==typeof s.beforeFetch&&s.beforeFetch(i,o)}catch(G){}}var c=Date.now();return id(e,o).then(function(e){for(var t=e.clone(),n=0;n<pd.length;n++){var r=pd[n];try{"function"==typeof r.then&&r.then(t,Date.now()-c,i,o,c)}catch(G){}}return t})["catch"](function(e){for(var t=0;t<pd.length;t++){var n=pd[t];try{"function"==typeof n["catch"]&&n["catch"](e,Date.now()-c,i,o,c)}catch(G){}}throw e})})},overrideXhr:function(b,w){var e,o,E=this,i={name:this.name,ignoreHackReg:null==(e=b.api)?void 0:e.ignoreHackReg,traceRequestHeader:null!=(e=b.api)&&e.injectTraceHeader?this.traceRequestHeader:null,send:function(h,p){var e,t,g=Date.now(),y=(((null==b?void 0:b.api)||{}).injectTraceHeader&&(t=(e=cd()).host,e=(t=E.traceRequestHeader.generate(h.aegisUrl,{},{host:t,pathname:e.pathname})||{}).name,t=t.value,e)&&t&&h.setRequestHeader(e,t),h.addEventListener("loadend",function(){var f,n,e,l,d=h.aegisUrl||"";tu(d,b.hostUrl)||"abort"===h.failType||(f="",(h.failType||!h.status||400<=h.status)&&(f=h.failType||"failed"),n=Date.now()-g,e=h.getResponseHeader("content-type")||"",l=vd(b,e,d),setTimeout(function(){var s,r=bd(b,{url:d,duration:n,type:l,status:h.status,method:h.aegisMethod||"get"});if("fetch"===l){var e=(null==(s=b.api)?void 0:s.reqHeaders)||[],o=gd(h.aegisXhrReqHeader,e,"req"),e=(null==(e=b.api)?void 0:e.resHeaders)||[],t=h.getAllResponseHeaders().split("\r\n").reduce(function(e,t){t=t.split(": ");return t[0]&&t[1]&&(e[t[0]]=t[1]),e},{}),i=gd(t,e,"res"),a=ns(h.aegisXhrReqHeader),e=null==(t=b.api)?void 0:t.apiDetail,u=e?za(p,null==(t=b.api)?void 0:t.reqParamHandler,{url:d}):"",c=e?za(h.response,null==(t=b.api)?void 0:t.resBodyHandler,{url:d}):"";try{!function(e,t,n,r){var o,i,a;try{if("function"==typeof(null==t?void 0:t.retCodeHandlerAsync))return t.retCodeHandlerAsync(e,null==n?void 0:n.url,null==n?void 0:n.ctx,function(e){var t=e.code;null!=r&&r({code:void 0===t?di:t,isErr:e.isErr})});if("function"==typeof(null==t?void 0:t.retCodeHandler))return a=(i=t.retCodeHandler(e,null==n?void 0:n.url,null==n?void 0:n.ctx,null==n?void 0:n.payload)||{}).code,null!=r&&r({code:void 0===a?di:a,isErr:i.isErr});if(!(e="string"==typeof e?JSON.parse(e):e))return null!=r&&r({code:di,isErr:!1});"function"==typeof(null==(o=null==t?void 0:t.ret)?void 0:o.join)&&(Ga=[].concat(t.ret.map(function(e){return e.toLowerCase()})));var u=Object.getOwnPropertyNames(e).filter(function(e){return-1!==Ga.indexOf(e.toLowerCase())});if(u.length)return"未知"!==(a=e[u[0]])&&""!==a||(a=di),null!=r&&r({code:""+a,isErr:0!==a&&"0"!==a&&a!==di});null!=r&&r({code:di,isErr:!1})}catch(s){null!=r&&r({code:di,isErr:!1})}}(h.response,b.api,{url:d,ctx:h,payload:p},function(e){var t=e.code,e=e.isErr,n=[f?"AJAX_ERROR: request "+f:"","fetch req url: "+d,"res status: "+(r.status||0),"res duration: "+r.duration+"ms","res startTime: "+g,o,i,"req method: "+(r.method||"GET"),"res retcode: "+t,yd(u,"req param"),yd(c,"res data")].filter(function(e){return e}).join("\n\n");r.ret=t,r.isErr=+e,r.payload=p,E.publishNormalLog({msg:n,level:md(!!f,e),code:t,trace:a},w),E.publishSpeed(r,w)})}catch(ee){r.ret=di,E.publishSpeed(r,w)}}else r.type="static",r.urlQuery=qa(d,!0),E.publishSpeed(r,w);h.removeEventListener("abort",y),h.removeEventListener("error",v),h.removeEventListener("timeout",m)},0))}),function(){h.failType="abort"}),v=function(){h.failType="error"},m=function(){h.failType="timeout"};h.addEventListener("abort",y),h.addEventListener("error",v),h.addEventListener("timeout",m)}};this.hackXHROptions=i,o=this.hackXHROptions,ld.find(function(e){return e.name===o.name})||(ld.push(o),!fd&&window.XMLHttpRequest&&(nd=window.XMLHttpRequest.prototype.send,rd=window.XMLHttpRequest.prototype.open,od=window.XMLHttpRequest.prototype.setRequestHeader,fd=!0,window.XMLHttpRequest.prototype.open=function(){this.aegisMethod=arguments[0];var e,t=arguments[1];if(dd.test(t)&&(t=""+location.origin+t),this.aegisUrl=t,null==(e=null==(t=o.ignoreHackReg)?void 0:t.test)||!e.call(t,this.aegisUrl))if(this.aegisXhrStartTime=Date.now(),this.sendByAegis)T.isIE()||(this.timeout=5e3);else for(var n=0;n<ld.length;n++){var r=ld[n];try{"function"==typeof r.open&&r.open(this)}catch(i){}}return rd.apply(this,arguments)},window.XMLHttpRequest.prototype.setRequestHeader=function(){var e,t,n;return null!=(n=null==(t=o.ignoreHackReg)?void 0:t.test)&&n.call(t,this.aegisUrl)?od.apply(this,arguments):(t=arguments[0],n=arguments[1],this.aegisXhrReqHeader=null!=(e=this.aegisXhrReqHeader)?e:{},null!=o&&o.traceRequestHeader&&-1<["traceparent","b3","sw8","sentry-trace"].indexOf(t)&&(this.aegisXhrReqHeader[t]||(arguments[1]=n),this.aegisXhrReqHeader[t])?void 0:(this.aegisXhrReqHeader[t]=arguments[1],od.apply(this,arguments)))},window.XMLHttpRequest.prototype.send=function(){var e,t;if(!(null!=(t=null==(e=o.ignoreHackReg)?void 0:e.test)&&t.call(e,this.aegisUrl)||this.sendByAegis))for(var n=0;n<ld.length;n++){var r=ld[n];try{"function"==typeof r.send&&r.send(this,arguments[0])}catch(E){}}return nd.apply(this,arguments)}))},publishSpeed:function(n){var r=this;this.$walk(function(e){var t=r.$getConfig(e);"fetch"!==n.type||"function"!=typeof(null==t?void 0:t.urlHandler)?(n.url=qa(n.url),e.speedLogPipeline(n)):e.speedLogPipeline(f(f({},n),{url:qa(t.urlHandler(n.url,n.payload))}))})},publishNormalLog:function(t){this.$walk(function(e){e.normalLogPipeline(t)})},destroy:function(){var t,n,e;this.option.publishSpeed=function(){},this.option.publishNormalLog=function(){},this.option.hackXHROptions&&(n=this.option.hackXHROptions,-1!==(e=ld.findIndex(function(e){return e.name===n.name})))&&ld.splice(e,1),this.option.hackFetchOptions&&(t=this.option.hackFetchOptions,-1!==(e=pd.findIndex(function(e){return e.name===t.name})))&&pd.splice(e,1),this.option.override=!1}})),wd={},e=new Iu({name:"reportBridgeSpeed",override:!1,onNewAegis:function(e){this.override||(this.override=!0,this.overrideBridge(e))},publishSpeed:function(t,n){this.$walk(function(e){e===n&&e.speedLogPipeline(t)})},overrideBridge:function(s){var c=this,f=s.config;f.reportBridgeSpeed&&f.h5Bridge&&f.h5BridgeFunc.length&&f.h5BridgeFunc.forEach(function(e){var u=f.h5Bridge[e];wd[e]=u,f.h5Bridge[e]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e[0],o=e[1],n=e[2],i=e[3],a=Date.now();u(r,o,n,function(e){var t=$a(e,f.api),n=t.code,t=t.isErr,n={url:r+"-"+o,name:r+"-"+o,duration:Date.now()-a,type:"bridge",ret:n,isErr:+t};c.publishSpeed(n,s),i(e)})}})},unHackBridge:function(t){Object.keys(wd).forEach(function(e){wd[e]&&(t.config.h5Bridge[e]=wd[e])}),wd={}},destroy:function(e){this.option.publishSpeed=function(){},this.option.unHackBridge(e),this.option.override=!1}});(p=ad=ad||{})[p.unknown=100]="unknown",p[p.wifi=1]="wifi",p[p.net2g=2]="net2g",p[p.net3g=3]="net3g",p[p.net4g=4]="net4g",p[p.net5g=5]="net5g",p[p.net6g=6]="net6g",(o=ud=ud||{})[o.android=1]="android",o[o.ios=2]="ios",o[o.windows=3]="windows",o[o.macos=4]="macos",o[o.linux=5]="linux",o[o.other=100]="other",(w=sd=sd||{})[w.unknown=100]="unknown",w[w.normal=0]="normal",w[w.weak=1]="weak",w[w.disconnected=2]="disconnected";var Ed,Rd,Sd,Od,Td,xd,Ad,Pd,Id,Ld,kd,Cd,jd,Ud,Nd,Md,go=new Iu({name:"device",onNewAegis:function(e){e.extendBean("platform",this.getPlatform()),e.extendBean("netType",ad.unknown),this.getDpi(e),this.refreshNetworkTypeToBean(e),this.refreshNetworkStatusToBean(e)},getDpi:function(e){e.extendBean("vp",Math.round(window.innerWidth)+" * "+Math.round(window.innerHeight)),window.screen&&e.extendBean("sr",Math.round(window.screen.width)+" * "+Math.round(window.screen.height))},getPlatform:function(){var t={android:/\bAndroid\s*([^;]+)/,ios:/\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/,windows:/\b(Windows NT)/,macos:/\b(Mac OS)/,linux:/\b(Linux)/i},e=Object.keys(t).find(function(e){return t[e].test(navigator.userAgent)});return e?ud[e]:ud.other},refreshNetworkTypeToBean:function(t){var n=this,e=t.config;e&&("function"==typeof e.getNetworkType?e.getNetworkType:_d)(function(e){ad[e]||(e=ad.unknown),t.extendBean("netType",e),n.NetworkRefreshTimer=setTimeout(function(){n.refreshNetworkTypeToBean(t),clearTimeout(n.NetworkRefreshTimer)},1e4)})},refreshNetworkStatusToBean:function(t){var e,n=this,r=t.config;r&&null!=(e="function"==typeof r.getNetworkStatus?r.getNetworkStatus:e)&&e(function(e){sd[e]===undefined&&(e=sd.unknown),t.extendBean("netStatus",e),n.NetworkStatusRefreshTimer=setTimeout(function(){n.refreshNetworkStatusToBean(t),clearTimeout(n.NetworkStatusRefreshTimer)},1e4)})}}),_d=function(e){var t="",n=navigator.userAgent.match(/NetType\/(\w+)/);n?t=n[1]:navigator.connection&&(t=navigator.connection.effectiveType||navigator.connection.type),e((n=t=t||di,0<=(n=String(n).toLowerCase()).indexOf("4g")?ad.net4g:0<=n.indexOf("wifi")?ad.wifi:0<=n.indexOf("5g")?ad.net5g:0<=n.indexOf("6g")?ad.net6g:0<=n.indexOf("3g")?ad.net3g:0<=n.indexOf("2g")?ad.net2g:ad.unknown))},Bd=window.WebSocket,Fd=[],qd={construct:function(e,t){var r=new e(t[0],t[1]);return r.originSend=r.send,r.addEventListener("error",function(e){var e=(null==e?void 0:e.currentTarget)||{},t=e.url,n=e.readyState;null!=Fd&&Fd.forEach(function(e){e=e.onErr;null!=e&&e({msg:"无法获知具体错误信息，需在浏览器控制台查看！",readyState:n,connectUrl:t})})}),Object.defineProperty(r,"send",{get:function(){return function(e){null!=(t=r.originSend)&&t.call(r,e);var t,e=r.readyState;if(e!==WebSocket.OPEN){var n={readyState:e,connectUrl:r.url};switch(e){case WebSocket.CLOSED:Fd.forEach(function(e){e=e.sendErr;null!=e&&e(f({msg:"消息发送失败，连接已关闭！"},n))});break;case WebSocket.CONNECTING:Fd.forEach(function(e){(0,e.sendErr)(f({msg:"消息发送失败，正在连接中！"},n))});break;case WebSocket.CLOSING:Fd.forEach(function(e){(0,e.sendErr)(f({msg:"消息发送失败，连接正在关闭！"},n))})}}}}}),r}},Dd=new Iu({name:"onError"}),js=Dd=new Iu({name:"onError",onNewAegis:function(e){this.startListen(e)},startListen:function(o){var e,t,n,r,i=this,a=window.onerror,u=(window.onerror=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Ya(e[0]);Ss(n)||i.publishErrorLog({msg:(n||"")+" @ ("+(Ya(e[1])||"")+":"+(e[2]||0)+":"+(e[3]||0)+")\n          \n"+Ya(e[4]||""),level:y.ERROR},o),null!=a&&a.call.apply(a,ai([window],e))},function(e){var t,n=e&&Ya(e.reason);n&&i.publishErrorLog({msg:"PROMISE_ERROR: "+n,errorMsg:null==(t=null==(e=(null==(t=e.reason)?void 0:t.message)||(null==(t=e.reason)?void 0:t.errMsg)||n)?void 0:e.slice)?void 0:t.call(e,0,150),level:y.PROMISE_ERROR},o)}),s=(window.addEventListener("unhandledrejection",u),function(e){var t,e=(null==e?void 0:e.target)||(null==e?void 0:e.srcElement);if(e){var n=e.src||e.href||"",e=e.tagName,e=void 0===e?"script":e;if(!(tu(t=n,o.config.hostUrl)||-1<window.location.href.indexOf(t))){var r={msg:e+" load fail: "+n,level:y.INFO};if(/\.js$/.test(n))r.level=y.SCRIPT_ERROR;else if(/\.css$/.test(n))r.level=y.CSS_ERROR;else switch(e.toLowerCase()){case"script":r.level=y.SCRIPT_ERROR;break;case"link":r.level=y.CSS_ERROR;break;case"img":r.level=y.IMAGE_ERROR;break;case"audio":case"video":r.level=y.MEDIA_ERROR;break;default:return}i.publishErrorLog(r,o)}}});window.document.addEventListener("error",s,!0),o.lifeCycle.on("destroy",function(){0===Dd.countInstance()&&(window.document.removeEventListener("unhandledrejection",u),window.document.removeEventListener("error",s,!0))}),o.config.websocketHack&&(e={key:o.config.id+"-"+this.name,onErr:function(e){var t;null!=(t=i.publishWsErrorLog)&&t.call(i,e,o)},sendErr:function(e){var t;null!=(t=i.publishWsErrorLog)&&t.call(i,e,o)}},this.hackWebsocketConfig=e,e=this.hackWebsocketConfig,window.Proxy)&&window.WebSocket&&(t=window.WebSocket,window&&!t.isHack&&(n=new Proxy(WebSocket,qd),t.isHack=!0,window.WebSocket=n),r=e,Fd.find(function(e){return e.key===r.key})||r&&Fd.push(r))},publishErrorLog:function(t,n){this.$walk(function(e){e===n&&e.normalLogPipeline(t)})},publishWsErrorLog:function(e,t){var n=e.connectUrl;this.publishErrorLog({msg:"WEBSOCKET_ERROR: \n              connect: "+n+"\n              readyState: "+e.readyState+"\n              msg: "+e.msg,level:y.WEBSOCKET_ERROR},t)},destroy:function(){var t,e;this.option.publishErrorLog=function(){},this.option.hackWebsocketConfig&&(t=this.option.hackWebsocketConfig,window.WebSocket=Bd,-1!==(e=Fd.findIndex(function(e){return e.key===t.key})))&&Fd.splice(e,1)}}),Hd=(new Iu({name:"pagePerformance"}),function(e,t,n,r){return void 0===n&&(n=15e3),void 0===r&&(r=0),(t=void 0===t?0:t)<=e&&e<=n?e:r}),Gs=new Iu({name:"pagePerformance",performanceMap:{},onNewAegis:function(e){Os()&&(Ed?this.publish(Ed,e):this.startCalcPerformance(e))},publish:function(e,t){var u=this;this.$walk(function(a){a===t&&a.sendPipeline([function(e,t){var n,r=[];for(n in e)r.push(n+"="+e[n]);var o,i=u.$getConfig(a);if(i)return o=-1===(null==(o=a.config.performanceUrl)?void 0:o.indexOf("?"))?"?":"&","function"==typeof i.urlHandler?t({url:a.config.performanceUrl+o+r.join("&")+"&from="+(encodeURIComponent(i.urlHandler())||window.location.href),beanFilter:["from"],type:v.PERFORMANCE,log:e}):t({url:a.config.performanceUrl+o+r.join("&"),type:v.PERFORMANCE,log:e})}],v.PERFORMANCE)(f({},e))})},startCalcPerformance:function(n){var r=this;try{this.getFirstScreenTiming(n,function(e){var t=performance.timing;t&&(Ed={dnsLookup:Hd(t.domainLookupEnd-t.domainLookupStart),tcp:Hd(t.connectEnd-t.connectStart),ssl:Hd(0===t.secureConnectionStart?0:t.requestStart-t.secureConnectionStart),ttfb:Hd(t.responseStart-t.requestStart),contentDownload:Hd(t.responseEnd-t.responseStart),domParse:Hd(t.domInteractive-t.domLoading,0,15e3,1070),resourceDownload:Hd(t.loadEventStart-t.domInteractive,0,15e3,1070),firstScreenTiming:Hd(Math.floor(e),0,15e3,15e3)},(t=n.config).extraPerformanceData&&"{}"!==JSON.stringify(t.extraPerformanceData)&&(t=(e=t.extraPerformanceData).engineInit,e=e.bundleLoad,Ed=f(f({},Ed),{engineInit:Hd(t,0,1e4),bundleLoad:Hd(e,0,1e4)})),r.publish(Ed,n))})}catch(M){}},getFirstScreenTiming:function(e,t){null!=t&&t(0)},setFirstScreenInfo:function(e,t,n,r){var o;e.config.id&&this.performanceMap[e.config.id]||(e.config.id&&(this.performanceMap[e.config.id]=!0),("object"!==vo(null==(o=e.config)?void 0:o.pagePerformance)||null!=(o=e.config.pagePerformance)&&o.firstScreenInfo)&&(e.firstScreenInfo={element:n,timing:t,markDoms:r}))},isEleInArray:function(e,t){return!(!e||e===document.documentElement)&&(-1!==t.indexOf(e)||this.isEleInArray(e.parentElement,t))},isInFirstScreen:function(e){var t;return!(!e||"function"!=typeof e.getBoundingClientRect)&&(e=e.getBoundingClientRect(),t=window.innerHeight,0<=e.left)&&e.left<window.innerWidth&&0<=e.top&&e.top<t&&0<e.width&&0<e.height},walkAndCount:function(e){var t=0;if(e&&1===e.nodeType){t+=1;var n=e.children;if(null!=n&&n.length)for(var r=0;r<n.length;r++)1===n[r].nodeType&&n[r].hasAttribute("AEGIS-IGNORE-FIRST-SCREEN-TIMING")||(t+=this.walkAndCount(n[r]))}return t},hasAncestorOrSelfWithAttribute:function(e,t){for(var n=e;n&&n!==document.body;){if(n.hasAttribute(t))return!0;n=n.parentElement}return!1}}),Wd=(xd={passive:!0,capture:!0},Ad=new Date,E=function(){Td=[],Sd=-1,Rd=null,kd(addEventListener)},Pd=function(e,t){Rd||(Rd=t,Sd=e,Od=new Date,kd(removeEventListener),Id())},Id=function(){var t;0<=Sd&&Sd<Od-Ad&&(t={entryType:"first-input",name:Rd.type,target:Rd.target,cancelable:Rd.cancelable,startTime:Rd.timeStamp,processingStart:Rd.timeStamp+Sd},Td.forEach(function(e){e(t)}),Td=[])},Ld=function(e){var t,n,r,o,i,a;e.cancelable&&(t=(1e12<e.timeStamp?new Date:performance.now())-e.timeStamp,"pointerdown"==e.type?(n=t,r=e,o=function(){Pd(n,r),a()},i=function(){a()},a=function(){removeEventListener("pointerup",o,xd),removeEventListener("pointercancel",i,xd)},addEventListener("pointerup",o,xd),addEventListener("pointercancel",i,xd)):Pd(t,e))},kd=function(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(e){return t(e,Ld,xd)})},Cd="hidden"===document.visibilityState?0:1/0,addEventListener("visibilitychange",function F(e){"hidden"===document.visibilityState&&(Cd=e.timeStamp,removeEventListener("visibilitychange",F,!0))},!0),E(),self.webVitals={firstInputPolyfill:function(e){Td.push(e),Id()},resetFirstInputPolyfill:E,get firstHiddenTime(){return Cd}},new Iu({name:"webVitals"}),new Iu({name:"spa"}),["replaceState","pushState","popstate","hashchange"]),Ws=new Iu({name:"spa",originFireUrl:"",onNewAegis:function(t){var n=this;history.pushState=this.wr("pushState")||history.pushState,history.replaceState=this.wr("replaceState")||history.replaceState,this.sendPv=this.sendPv.bind(this),t.config.spa&&this.sendPv(t),Wd.forEach(function(e){return window.addEventListener(e,function(){return n.sendPv.call(n,t)})})},wr:function(n){var r=history[n],e="__"+n+"__hasWrittenByTamSpa";return"function"==typeof r&&!history[e]&&(Object.defineProperty(history,e,{value:!0,enumerable:!1}),function(){var e=r.apply(this,arguments),t=null;return"function"==typeof Event?t=new Event(n):(t=document.createEvent("HTMLEvents")).initEvent(n,!1,!0),window.dispatchEvent(t),e})},sendPv:function(r){var o=this;setTimeout(function(){var t=location.pathname+location.hash+r.config.id;o.$walk(function(e){var n;e===r&&(n=e.config.pvUrl)&&t&&t!==o.originFireUrl&&(e.sendPipeline([function(e,t){t({url:""+n,type:v.PV})}],v.PV)(null),o.originFireUrl=t)})},0)},destroy:function(){this.option.sendPv=function(){}}}),Vd=(ii(Ud=Th,n=jd=l),Ud.prototype=null===n?Object.create(n):(xh.prototype=n.prototype,new xh),Th.prototype.getBean=function(t){var n=this;return void 0===t&&(t=[]),""+Object.getOwnPropertyNames(this.bean).filter(function(e){return-1===t.indexOf(e)}).map(function(e){return"from"===e?"from="+n.getCurrentPageUrl():e+"="+n.bean[e]}).join("&")},Th.prototype.getCurrentPageUrl=function(){var e=this.config.pageUrl||location.href,e=(e="function"==typeof this.config.urlHandler?this.config.urlHandler()||location.href:e).slice(0,2048);return encodeURIComponent(e)},Th.prototype.ready=function(){var t=this,i=function(){var e,n,r,o;t.reportRequestQueue.length&&(e=t.reportRequestQueue.splice(0,1)[0],n=e.options,r=e.success,o=e.fail,t.$request(n,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return null==r?void 0:r.apply(n,e)}finally{i()}},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return null==o?void 0:o.apply(n,e)}finally{i()}}))};i(),this.isReportReady=!0},Th.prototype.request=function(e,t,n){this.config.reportImmediately||this.isReportReady?this.$request(e,t,n):this.reportRequestQueue.push({options:e,success:t,fail:n})},Th.prototype.$request=function(u,e,t){var n,r,o,i;if(u&&"string"==typeof u.url&&""!==u.url&&this.bean.id)return i=u.url,!1!==u.addBean&&(i=function(e,t){var n,r,o,i=e,a=u.beanFilter||[];for(n in t)-1===a.indexOf(n)&&((o=(r=new RegExp("([?&])"+n+"(=([^&]*))?(?=&|$)")).exec(i))?o[2]&&""!==o[3]&&"undefined"!==o[3]||(i=i.replace(r,"$1"+n+"="+t[n])):(o=-1<i.indexOf("?")?"&":"?",i+=o+n+"="+t[n]));return i}(i,f(f({},this.bean),{from:this.getCurrentPageUrl()}))),u.url=i,i=u.method||"get",r=this.config.onBeforeRequest,(u=r?r(u,this):u)?u.url?void((null!=u&&u.sendBeacon||this.sendNow)&&"function"==typeof(null==navigator?void 0:navigator.sendBeacon)?navigator.sendBeacon(u.url,u.data):((n=new XMLHttpRequest).sendByAegis=!0,n.addEventListener("readystatechange",function(){4===n.readyState&&(400<=n.status||0===n.status?null!=t&&t(n.response):null!=e&&e(n.response))}),"get"===i.toLocaleLowerCase()?(n.open("get",(r=u.url,o=u.data,"string"!=typeof r?"":"object"===vo(o)&&o?(i=Object.getOwnPropertyNames(o).map(function(e){var t=o[e];return e+"="+("string"==typeof t?encodeURIComponent(t):encodeURIComponent(JSON.stringify(t)))}).join("&").replace(/eval/gi,"evaI"),r+(-1===r.indexOf("?")?"?":"&")+i):r)),n.send()):(n.open("post",u.url),u.contentType&&n.setRequestHeader("Content-Type",u.contentType),"string"==typeof u.data&&(u.data=u.data.replace(/eval/gi,"evaI")),n.send(u.data)))):console.warn("Please handle the parameters reasonably, options.url is necessary"):console.warn("Sending request blocked")},Th.prototype.publishPluginsLogs=function(){var e=Th.installedPlugins.find(function(e){return"reportAssetSpeed"===e.name});null!=e&&e.option.collectNotReportedLog(this)},Th.prototype.uploadLogs=function(e,t){var n;void 0===e&&(e={}),void 0===t&&(t={}),null!=(n=this.lifeCycle)&&n.emit("uploadLogs",e,t)},Th.sessionID="session-"+Date.now(),Th),p=(new Iu({name:"ie"}),new Iu({name:"ie",init:function(){this.overrideAegisSend()},onNewAegis:function(e){this.startListen(e)},startListen:function(r){var o=this,i=window.onerror;window.onerror=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Ya(e[0]);Ss(n)||o.publishErrorLog({msg:(n||"")+" @ ("+(e[1]||"")+":"+(e[2]||0)+":"+(e[3]||0)+")\n          \n"+Ya(e[4]||""),level:y.ERROR},r),null!=i&&i.call.apply(i,ai([window],e))}},overrideAegisSend:function(){Vd.prototype.request=function(e,t,n){var r,o,i=this;e&&"string"==typeof e.url&&""!==e.url&&this.bean.id&&(r=e.url,!1!==e.addBean&&(r=r+(-1===r.indexOf("?")?"?":"&")+this.getBean(e.beanFilter)),(o=new Image).onload=function(){i.request(e,t,n),null!=t&&t(null)},o.onerror=function(){i.request(e,t,n),null!=n&&n(null)},e.data?o.src=r+"&"+e.data:o.src=r)}},publishErrorLog:function(t,n){this.$walk(function(e){e===n&&e.normalLogPipeline(t)})},destroy:function(){this.option.publishErrorLog=function(){}}})),o=(new Iu({name:"onClose"}),new Iu({name:"onClose",onNewAegis:function(n){var e,t,r,o,i,a=this;T.desktop()?(i=window.onunload,window.onunload=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];a.publishNotReportedLog(n),null!=i&&i.call.apply(i,ai([window],e))}):((o=function(t,n,r,o){var i=function(e){"visibilitychange"===t&&"hidden"!==document.visibilityState||(o?setTimeout(function(){return n(e)},o):n(e),r&&removeEventListener(t,i,!0))};addEventListener(t,i,!0)})("visibilitychange",e=this.publishNotReportedLog.bind(this,n),null==(t={once:!0})?void 0:t.once,null==(r=null==t?void 0:t.delay)?void 0:r.visibilitychange),o("pagehide",e,null==t?void 0:t.once,null==(r=null==t?void 0:t.delay)?void 0:r.pagehide))},publishNotReportedLog:function(t){var n=this;this.$walk(function(e){e===t&&(e.sendNow=!0,e.publishPluginsLogs(),n.publishThrottlePipeLogs(e))})},publishThrottlePipeLogs:function(e){null!=e&&e.speedLogPipeline([]),null!=e&&e.eventPipeline([]),null!=e&&e.customTimePipeline([]),null!=e&&e.normalLogPipeline([])}})),w="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,Gd=RangeError,$d=function(e){if(e===undefined)return 0;var e=ct(e),t=pt(e);if(e!==t)throw new Gd("Wrong length or index");return t},zd=Math.sign||function(e){e=+e;return 0==e||e!=e?e:e<0?-1:1},Xd=Math.abs,Yd=Math.fround||function(e){return t=1.1920928955078125e-7,n=11754943508222875e-54,r=Xd(e=+e),e=zd(e),r<n?e*(4503599627370496+r/n/t-4503599627370496)*n*t:34028234663852886e22<(t=(n=536870913*r)-(n-r))||t!=t?e*Infinity:e*t;var t,n,r},Jd=Array,Kd=Math.abs,Qd=Math.pow,Zd=Math.floor,eh=Math.log,th=Math.LN2,nh=function(e){for(var t=we(this),n=gt(t),r=arguments.length,o=dt(1<r?arguments[1]:undefined,n),r=2<r?arguments[2]:undefined,i=r===undefined?n:dt(r,n);o<i;)t[o++]=e;return t},E=Xe.PROPER,rh=Xe.CONFIGURABLE,oh=k.getterFor("ArrayBuffer"),ih=k.getterFor("DataView"),ah=k.set,uh=x.ArrayBuffer,sh=uh,ch=sh&&sh.prototype,l=x.DataView,fh=l&&l.prototype,n=Object.prototype,lh=x.Array,dh=x.RangeError,hh=h(nh),ph=h([].reverse),gh=function(e,t,n){var r,o,i,a=Jd(n),u=8*n-t-1,n=(1<<u)-1,s=n>>1,c=23===t?Qd(2,-24)-Qd(2,-77):0,f=e<0||0===e&&1/e<0?1:0,l=0;for((e=Kd(e))!=e||e===Infinity?(o=e!=e?1:0,r=n):(r=Zd(eh(e)/th),e*(i=Qd(2,-r))<1&&(r--,i*=2),2<=(e+=1<=r+s?c/i:c*Qd(2,1-s))*i&&(r++,i/=2),n<=r+s?(o=0,r=n):1<=r+s?(o=(e*i-1)*Qd(2,t),r+=s):(o=e*Qd(2,s-1)*Qd(2,t),r=0));8<=t;)a[l++]=255&o,o/=256,t-=8;for(r=r<<t|o,u+=t;0<u;)a[l++]=255&r,r/=256,u-=8;return a[l-1]|=128*f,a},yh=function(e,t){var n,r=e.length,o=8*r-t-1,i=(1<<o)-1,a=i>>1,u=o-7,s=r-1,o=e[s--],c=127&o;for(o>>=7;0<u;)c=256*c+e[s--],u-=8;for(n=c&(1<<-u)-1,c>>=-u,u+=t;0<u;)n=256*n+e[s--],u-=8;if(0===c)c=1-a;else{if(c===i)return n?NaN:o?-Infinity:Infinity;n+=Qd(2,t),c-=a}return(o?-1:1)*n*Qd(2,c-t)},vh=function(e){return[255&e]},mh=function(e){return[255&e,e>>8&255]},bh=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},wh=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},Eh=function(e){return gh(Yd(e),23,4)},Rh=function(e){return gh(e,52,8)},j=function(e,t,n){c(e.prototype,t,{configurable:!0,get:function(){return n(this)[t]}})},Sh=function(e,t,n,r){e=ih(e),n=$d(n),r=!!r;if(n+t>e.byteLength)throw new dh("Wrong index");n+=e.byteOffset,e=m(e.bytes,n,n+t);return r?e:ph(e)},Oh=function(e,t,n,r,o,i){var e=ih(e),n=$d(n),a=r(+o),u=!!i;if(n+t>e.byteLength)throw new dh("Wrong index");for(var s=e.bytes,c=n+e.byteOffset,f=0;f<t;f++)s[c+f]=a[u?f:t-f-1]};function Th(e){var t,n,r,a,u=jd.call(this,e)||this;u.sendNow=!1,u.isReportReady=!1,u.reportRequestQueue=[],u.speedLogPipeline=os([hu(u),(a=u.config,function(e,t){var n,r,o,i="number"==typeof a.repeat?a.repeat:60;!a.speedSample||i<=0?t(e):(n=(null==a?void 0:a.id)||"0",r=mu[n]||{},Array.isArray(e)?(o=e.filter(function(e){var t=!r[e.url]||r[e.url]<i;return t?(r[e.url]=1+~~r[e.url],mu[n]=r):vu[n]||bu(n),t})).length&&t(o):!r[e.url]||r[e.url]<i?(r[e.url]=1+~~r[e.url],mu[n]=r,t(e)):vu[n]||bu(n))}),(r=u,function(t,n){_d(function(e){r.extendBean("netType",e),n(t)})}),function(e,t){null!=(n=u.lifeCycle)&&n.emit("beforeReportSpeed",e);var n,r=u.config.beforeReportSpeed;if((e="function"==typeof r?e.filter(function(e){return!1!==r(e)}):e).length)return t(e)},function(e,t){t(e.map(function(e){return"undefined"!=typeof e.payload&&delete e.payload,e}))},function(e){return u.sendPipeline([function(e,t){var n,r,o,i,a;t({type:v.SPEED,url:""+u.config.speedUrl,method:"post",data:(n=e,r=f(f({},u.bean),{from:u.getCurrentPageUrl()}),i={fetch:[],"static":[],bridge:[]},a=new FormData,Array.isArray(n)?n.forEach(function(e){var t=Rs(e);i[e.type].push(t)}):(o=Rs(n),i[n.type].push(o)),a.append("payload",Ja(f({duration:i},r))),a),log:e})}],v.SPEED)(e)}]),e.asyncPlugin=!0;try{"undefined"!=typeof document&&(e.uin=e.uin||(null!=(t=document.cookie.match(/\buin=\D+(\d*)/))?t:[])[1]||(null!=(n=document.cookie.match(/\bilive_uin=\D*(\d+)/))?n:[])[1]||""),u.init(e),u.extendBean("sessionId",Th.sessionID),u.extendBean("from",u.getCurrentPageUrl()),"undefined"!=typeof document&&u.extendBean("referer",encodeURIComponent(document.referrer||"")),e.ext1&&u.extendBean("ext1",encodeURIComponent(e.ext1)),e.ext2&&u.extendBean("ext2",encodeURIComponent(e.ext2)),e.ext3&&u.extendBean("ext3",encodeURIComponent(e.ext3))}catch(A){console.warn(A),console.log("%cThe above error occurred in the process of initializing Aegis, which will affect your normal use of Aegis.\nIt is recommended that you contact us for feedback and thank you for your support.","color: red"),u.sendSDKError(A)}return u}function xh(){this.constructor=Ud}w?(Nd=E&&"ArrayBuffer"!==uh.name,d(function(){uh(1)})&&d(function(){new uh(-1)})&&!d(function(){return new uh,new uh(1.5),new uh(NaN),1!==uh.length||Nd&&!rh})?Nd&&rh&&Ge(uh,"name","ArrayBuffer"):(((sh=function(e){return Rr(this,ch),Nr(new uh($d(e)),this,sh)}).prototype=ch).constructor=sh,Tt(sh,uh)),Cn&&Rn(fh)!==n&&Cn(fh,n),E=new l(new sh(2)),Md=h(fh.setInt8),E.setInt8(0,2147483648),E.setInt8(1,2147483649),!E.getInt8(0)&&E.getInt8(1)||Mr(fh,{setInt8:function(e,t){Md(this,e,t<<24>>24)},setUint8:function(e,t){Md(this,e,t<<24>>24)}},{unsafe:!0})):(ch=(sh=function(e){Rr(this,ch);e=$d(e);ah(this,{type:"ArrayBuffer",bytes:hh(lh(e),0),byteLength:e}),A||(this.byteLength=e,this.detached=!1)}).prototype,fh=(l=function(e,t,n){Rr(this,fh),Rr(e,ch);var r=oh(e),o=r.byteLength,t=ct(t);if(t<0||o<t)throw new dh("Wrong offset");if(t+(n=n===undefined?o-t:pt(n))>o)throw new dh("Wrong length");ah(this,{type:"DataView",buffer:e,byteLength:n,byteOffset:t,bytes:r.bytes}),A||(this.buffer=e,this.byteLength=n,this.byteOffset=t)}).prototype,A&&(j(sh,"byteLength",oh),j(l,"buffer",ih),j(l,"byteLength",ih),j(l,"byteOffset",ih)),Mr(fh,{getInt8:function(e){return Sh(this,1,e)[0]<<24>>24},getUint8:function(e){return Sh(this,1,e)[0]},getInt16:function(e){e=Sh(this,2,e,1<arguments.length&&arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(e){e=Sh(this,2,e,1<arguments.length&&arguments[1]);return e[1]<<8|e[0]},getInt32:function(e){return wh(Sh(this,4,e,1<arguments.length&&arguments[1]))},getUint32:function(e){return wh(Sh(this,4,e,1<arguments.length&&arguments[1]))>>>0},getFloat32:function(e){return yh(Sh(this,4,e,1<arguments.length&&arguments[1]),23)},getFloat64:function(e){return yh(Sh(this,8,e,1<arguments.length&&arguments[1]),52)},setInt8:function(e,t){Oh(this,1,e,vh,t)},setUint8:function(e,t){Oh(this,1,e,vh,t)},setInt16:function(e,t){Oh(this,2,e,mh,t,2<arguments.length&&arguments[2])},setUint16:function(e,t){Oh(this,2,e,mh,t,2<arguments.length&&arguments[2])},setInt32:function(e,t){Oh(this,4,e,bh,t,2<arguments.length&&arguments[2])},setUint32:function(e,t){Oh(this,4,e,bh,t,2<arguments.length&&arguments[2])},setFloat32:function(e,t){Oh(this,4,e,Eh,t,2<arguments.length&&arguments[2])},setFloat64:function(e,t){Oh(this,8,e,Rh,t,2<arguments.length&&arguments[2])}})),xn(sh,"ArrayBuffer"),xn(l,"DataView");var Ah={ArrayBuffer:sh,DataView:l},n=Ah.ArrayBuffer,E=x.ArrayBuffer,Ph=(C({global:!0,constructor:!0,forced:E!==n},{ArrayBuffer:n}),xi("ArrayBuffer"),Ah.ArrayBuffer),Ih=Ah.DataView,j=Ih.prototype,Lh=$e(Ph.prototype.slice),kh=$e(j.getUint8),Ch=$e(j.setUint8),l=d(function(){return!new Ph(2).slice(1,undefined).byteLength}),jh=(C({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:l},{slice:function(e,t){if(Lh&&t===undefined)return Lh(S(this),e);for(var n=S(this).byteLength,r=dt(e,n),o=dt(t===undefined?n:t,n),e=new Ph(pt(o-r)),i=new Ih(this),a=new Ih(e),u=0;r<o;)Ch(a,u++,kh(i,r++));return e}}),C({global:!0,constructor:!0,forced:!w},{DataView:Ah.DataView}),Math.log),Uh=Math.log1p||function(e){e=+e;return-1e-8<e&&e<1e-8?e-e*e/2:jh(1+e)},E=Math.acosh,Nh=Math.log,Mh=Math.sqrt,_h=Math.LN2,n=!E||710!==Math.floor(E(Number.MAX_VALUE))||E(Infinity)!==Infinity,j=(C({target:"Math",stat:!0,forced:n},{acosh:function(e){e=+e;return e<1?NaN:94906265.62425156<e?Nh(e)+_h:Uh(e-1+Mh(e-1)*Mh(1+e))}}),Math.asinh),Bh=Math.log,Fh=Math.sqrt,l=!(j&&0<1/j(0)),E=(C({target:"Math",stat:!0,forced:l},{asinh:function Gg(e){e=+e;return isFinite(e)&&0!=e?e<0?-Gg(-e):Bh(e+Fh(e*e+1)):e}}),Math.atanh),qh=Math.log,n=!(E&&1/E(-0)<0),j=(C({target:"Math",stat:!0,forced:n},{atanh:function(e){e=+e;return 0==e?e:qh((1+e)/(1-e))/2}}),Math.expm1),Dh=Math.exp,Hh=!j||22025.465794806718<j(10)||j(10)<22025.465794806718||-2e-17!==j(-2e-17)?function(e){e=+e;return 0==e?e:-1e-6<e&&e<1e-6?e+e*e/2:Dh(e)-1}:j,l=Math.cosh,Wh=Math.abs,Vh=Math.E,E=!l||l(710)===Infinity,Gh=(C({target:"Math",stat:!0,forced:E},{cosh:function(e){e=Hh(Wh(e)-1)+1;return(e+1/(e*Vh*Vh))*(Vh/2)}}),C({target:"Math",stat:!0,forced:Hh!==Math.expm1},{expm1:Hh}),C({target:"Math",stat:!0},{log1p:Uh}),Math.abs),$h=Math.exp,zh=Math.E,n=d(function(){return-2e-17!==Math.sinh(-2e-17)}),Xh=(C({target:"Math",stat:!0,forced:n},{sinh:function(e){e=+e;return Gh(e)<1?(Hh(e)-Hh(-e))/2:($h(e-1)-$h(-e-1))*(zh/2)}}),Math.exp),Yh=(C({target:"Math",stat:!0},{tanh:function(e){var e=+e,t=Hh(e),n=Hh(-e);return t===Infinity?1:n===Infinity?-1:(t-n)/(Xh(e)+Xh(-e))}}),h(H.f)),Jh=h([].push),Kh=A&&d(function(){var e=Object.create(null);return e[2]=2,!Yh(e,2)}),j=function(s){return function(e){for(var t,n=ee(e),r=en(n),o=Kh&&null===Rn(n),i=r.length,a=0,u=[];a<i;)t=r[a++],A&&!(o?t in n:Yh(n,t))||Jh(u,s?[t,n[t]]:n[t]);return u}},Qh=(j(!0),j(!1));C({target:"Object",stat:!0},{values:function(e){return Qh(e)}});var Zh,ep,tp,np=k.enforce,rp=k.get,l=x.Int8Array,op=l&&l.prototype,E=x.Uint8ClampedArray,n=E&&E.prototype,ip=l&&Rn(l),ap=op&&Rn(op),j=Object.prototype,up=x.TypeError,E=s("toStringTag"),sp=Te("TYPED_ARRAY_TAG"),cp=w&&!!Cn&&"Opera"!==Ft(x.opera),l=!1,fp={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},lp={BigInt64Array:8,BigUint64Array:8},dp=function(e){var t,e=Rn(e);if(I(e))return(t=rp(e))&&L(t,"TypedArrayConstructor")?t.TypedArrayConstructor:dp(e)},hp=function(e){return!!I(e)&&(e=Ft(e),L(fp,e)||L(lp,e))};for(Zh in fp)(tp=(ep=x[Zh])&&ep.prototype)?np(tp).TypedArrayConstructor=ep:cp=!1;for(Zh in lp)(tp=(ep=x[Zh])&&ep.prototype)&&(np(tp).TypedArrayConstructor=ep);if((!cp||!R(ip)||ip===Function.prototype)&&(ip=function(){throw new up("Incorrect invocation")},cp))for(Zh in fp)x[Zh]&&Cn(x[Zh],ip);if((!cp||!ap||ap===j)&&(ap=ip.prototype,cp))for(Zh in fp)x[Zh]&&Cn(x[Zh].prototype,ap);if(cp&&Rn(n)!==ap&&Cn(n,ap),A&&!L(ap,E))for(Zh in c(ap,E,{configurable:l=!0,get:function(){return I(this)?this[sp]:undefined}}),fp)x[Zh]&&Ge(x[Zh],sp,Zh);var U={NATIVE_ARRAY_BUFFER_VIEWS:cp,TYPED_ARRAY_TAG:l&&sp,aTypedArray:function(e){if(hp(e))return e;throw new up("Target is not a typed array")},aTypedArrayConstructor:function(e){if(!R(e)||Cn&&!re(ip,e))throw new up(le(e)+" is not a typed array constructor");return e},exportTypedArrayMethod:function(e,t,n,r){if(A){if(n)for(var o in fp){o=x[o];if(o&&L(o.prototype,e))try{delete o.prototype[e]}catch(N){try{o.prototype[e]=t}catch(q){}}}ap[e]&&!n||g(ap,e,!n&&cp&&op[e]||t,r)}},exportTypedArrayStaticMethod:function(e,t,n){var r,o;if(A){if(Cn){if(n)for(r in fp)if((o=x[r])&&L(o,e))try{delete o[e]}catch(N){}if(ip[e]&&!n)return;try{return g(ip,e,!n&&cp&&ip[e]||t)}catch(N){}}for(r in fp)!(o=x[r])||o[e]&&!n||g(o,e,t)}},getTypedArrayConstructor:dp,isView:function(e){return!!I(e)&&("DataView"===(e=Ft(e))||L(fp,e)||L(lp,e))},isTypedArray:hp,TypedArray:ip,TypedArrayPrototype:ap},pp=x.ArrayBuffer,gp=x.Int8Array,yp=!U.NATIVE_ARRAY_BUFFER_VIEWS||!d(function(){gp(1)})||!d(function(){new gp(-1)})||!Wn(function(e){new gp,new gp(null),new gp(1.5),new gp(e)},!0)||d(function(){return 1!==new gp(new pp(2),1,undefined).length}),vp=Math.floor,mp=Number.isInteger||function(e){return!I(e)&&isFinite(e)&&vp(e)===e},bp=RangeError,wp=RangeError,Ep=function(t,e){var n=function(){var e=ct(t);if(e<0)throw new bp("The argument can't be less than 0");return e}();if(n%e)throw new wp("Wrong offset");return n},Rp=Math.round,Sp=TypeError,Op=function(e){e=ke(e,"number");if("number"==typeof e)throw new Sp("Can't convert number to bigint");return BigInt(e)},Tp=U.aTypedArrayConstructor,xp=function(e){var t,n,r,o,i,a,u,s,c=ks(this),f=we(e),e=arguments.length,l=1<e?arguments[1]:undefined,d=l!==undefined,h=hr(f);if(h&&!lr(h))for(s=(u=gr(f,h)).next,f=[];!(a=P(s,u)).done;)f.push(a.value);for(d&&2<e&&(l=Ut(l,arguments[2])),n=gt(f),o=function(e){e=Ft(e);return"BigInt64Array"===e||"BigUint64Array"===e}(r=new(Tp(c))(n)),t=0;t<n;t++)i=d?l(f[t],t):f[t],r[t]=o?Op(i):+i;return r},Ap=function(e,t,n){for(var r=0,o=2<arguments.length?n:gt(t),i=new e(o);r<o;)i[r]=t[r++];return i},w=t(function(e){var u=Et.f,s=Zt.forEach,h=k.get,p=k.set,g=k.enforce,y=Ve.f,n=Be.f,v=x.RangeError,m=Ah.ArrayBuffer,t=m.prototype,b=Ah.DataView,w=U.NATIVE_ARRAY_BUFFER_VIEWS,E=U.TYPED_ARRAY_TAG,R=U.TypedArray,S=U.TypedArrayPrototype,O=U.isTypedArray,r=function(e,t){c(e,t,{configurable:!0,get:function(){return h(this)[t]}})},T=function(e){return re(t,e)||"ArrayBuffer"===(e=Ft(e))||"SharedArrayBuffer"===e},o=function(e,t){return O(e)&&!ce(t)&&t in e&&mp(+t)&&0<=t},i=function(e,t){return t=Ce(t),o(e,t)?W(2,e[t]):n(e,t)},a=function(e,t,n){return t=Ce(t),!(o(e,t)&&I(n)&&L(n,"value"))||L(n,"get")||L(n,"set")||n.configurable||L(n,"writable")&&!n.writable||L(n,"enumerable")&&!n.enumerable?y(e,t,n):(e[t]=n.value,e)};A?(w||(Be.f=i,Ve.f=a,r(S,"buffer"),r(S,"byteOffset"),r(S,"byteLength"),r(S,"length")),C({target:"Object",stat:!0,forced:!w},{getOwnPropertyDescriptor:i,defineProperty:a}),e.exports=function(e,t,r){var c=e.match(/\d+/)[0]/8,n=e+(r?"Clamped":"")+"Array",o="get"+e,i="set"+e,a=x[n],f=a,l=f&&f.prototype,e={},d=function(e,n){y(e,n,{get:function(){var e=this,t=n;return(e=h(e)).view[o](t*c+e.byteOffset,!0)},set:function(e){return function(e,t,n){e=h(e);e.view[i](t*c+e.byteOffset,r?(t=Rp(n))<0?0:255<t?255:255&t:n,!0)}(this,n,e)},enumerable:!0})},t=(w?yp&&(f=t(function(e,t,n,r){return Rr(e,l),Nr(I(t)?T(t)?r!==undefined?new a(t,Ep(n,c),r):n!==undefined?new a(t,Ep(n,c)):new a(t):O(t)?Ap(f,t):P(xp,f,t):new a($d(t)),e,f)}),Cn&&Cn(f,R),s(u(a),function(e){e in f||Ge(f,e,a[e])}),f.prototype=l):(f=t(function(e,t,n,r){Rr(e,l);var o,i,a=0,u=0;if(I(t)){if(!T(t))return O(t)?Ap(f,t):P(xp,f,t);var s=t,u=Ep(n,c),n=t.byteLength;if(r===undefined){if(n%c)throw new v("Wrong length");if((o=n-u)<0)throw new v("Wrong length")}else if((o=pt(r)*c)+u>n)throw new v("Wrong length");i=o/c}else i=$d(t),s=new m(o=i*c);for(p(e,{buffer:s,byteOffset:u,byteLength:o,length:i,view:new b(s)});a<i;)d(e,a++)}),Cn&&Cn(f,R),l=f.prototype=cn(S)),l.constructor!==f&&Ge(l,"constructor",f),g(l).TypedArrayConstructor=f,E&&Ge(l,E,n),f!==a);e[n]=f,C({global:!0,constructor:!0,forced:t,sham:!w},e),"BYTES_PER_ELEMENT"in f||Ge(f,"BYTES_PER_ELEMENT",c),"BYTES_PER_ELEMENT"in l||Ge(l,"BYTES_PER_ELEMENT",c),xi(n)}):e.exports=function(){}}),Pp=(w("Float32",function(r){return function(e,t,n){return r(this,e,t,n)}}),w("Uint8",function(r){return function(e,t,n){return r(this,e,t,n)}}),Math.min),Ip=h([].copyWithin||function(e,t){var n=we(this),r=gt(n),o=dt(e,r),i=dt(t,r),e=2<arguments.length?arguments[2]:undefined,a=Pp((e===undefined?r:dt(e,r))-i,r-o),u=1;for(i<o&&o<i+a&&(u=-1,i+=a-1,o+=a-1);0<a--;)i in n?n[o]=n[i]:Co(n,o),o+=u,i+=u;return n}),Lp=U.aTypedArray,kp=((0,U.exportTypedArrayMethod)("copyWithin",function(e,t){return Ip(Lp(this),e,t,2<arguments.length?arguments[2]:undefined)}),Zt.every),Cp=U.aTypedArray,jp=((0,U.exportTypedArrayMethod)("every",function(e){return kp(Cp(this),e,1<arguments.length?arguments[1]:undefined)}),U.aTypedArray),j=U.exportTypedArrayMethod,Up=h("".slice),Np=(j("fill",function(e){var t=arguments.length,e=(jp(this),"Big"===Up(Ft(this),0,3)?Op(e):+e);return P(nh,this,e,1<t?arguments[1]:undefined,2<t?arguments[2]:undefined)},d(function(){var e=0;return new Int8Array(2).fill({valueOf:function(){return e++}}),1!==e})),U.getTypedArrayConstructor),Mp=Zt.filter,_p=U.aTypedArray,Bp=((0,U.exportTypedArrayMethod)("filter",function(e){var e=Mp(_p(this),e,1<arguments.length?arguments[1]:undefined);return e=e,Ap(Np(this),e)}),Zt.find),Fp=U.aTypedArray,qp=((0,U.exportTypedArrayMethod)("find",function(e){return Bp(Fp(this),e,1<arguments.length?arguments[1]:undefined)}),Zt.findIndex),Dp=U.aTypedArray,Hp=((0,U.exportTypedArrayMethod)("findIndex",function(e){return qp(Dp(this),e,1<arguments.length?arguments[1]:undefined)}),Zt.forEach),Wp=U.aTypedArray,Vp=((0,U.exportTypedArrayMethod)("forEach",function(e){Hp(Wp(this),e,1<arguments.length?arguments[1]:undefined)}),ze.includes),Gp=U.aTypedArray,$p=((0,U.exportTypedArrayMethod)("includes",function(e){return Vp(Gp(this),e,1<arguments.length?arguments[1]:undefined)}),ze.indexOf),zp=U.aTypedArray,Xp=((0,U.exportTypedArrayMethod)("indexOf",function(e){return $p(zp(this),e,1<arguments.length?arguments[1]:undefined)}),s("iterator")),n=x.Uint8Array,Yp=h(Hn.values),Jp=h(Hn.keys),Kp=h(Hn.entries),Qp=U.aTypedArray,E=U.exportTypedArrayMethod,Zp=n&&n.prototype,l=!d(function(){Zp[Xp].call([1])}),Wn=!!Zp&&Zp.values&&Zp[Xp]===Zp.values&&"values"===Zp.values.name,w=function(){return Yp(Qp(this))},eg=(E("entries",function(){return Kp(Qp(this))},l),E("keys",function(){return Jp(Qp(this))},l),E("values",w,l||!Wn,{name:"values"}),E(Xp,w,l||!Wn,{name:"values"}),U.aTypedArray),j=U.exportTypedArrayMethod,tg=h([].join),ng=(j("join",function(e){return tg(eg(this),e)}),Math.min),rg=[].lastIndexOf,og=!!rg&&1/[1].lastIndexOf(1,-0)<0,ze=$t("lastIndexOf"),ig=og||!ze?function(e){if(og)return _o(rg,this,arguments)||0;var t=ee(this),n=gt(t);if(0!==n){var r=n-1;for((r=1<arguments.length?ng(r,ct(arguments[1])):r)<0&&(r=n+r);0<=r;r--)if(r in t&&t[r]===e)return r||0}return-1}:rg,ag=U.aTypedArray,ug=((0,U.exportTypedArrayMethod)("lastIndexOf",function(e){var t=arguments.length;return _o(ig,ag(this),1<t?[e,arguments[1]]:[e])}),Zt.map),sg=U.aTypedArray,cg=U.getTypedArrayConstructor,fg=((0,U.exportTypedArrayMethod)("map",function(e){return ug(sg(this),e,1<arguments.length?arguments[1]:undefined,function(e,t){return new(cg(e))(t)})}),no.left),lg=U.aTypedArray,dg=((0,U.exportTypedArrayMethod)("reduce",function(e){var t=arguments.length;return fg(lg(this),e,t,1<t?arguments[1]:undefined)}),no.right),hg=U.aTypedArray,pg=((0,U.exportTypedArrayMethod)("reduceRight",function(e){var t=arguments.length;return dg(hg(this),e,t,1<t?arguments[1]:undefined)}),U.aTypedArray),n=U.exportTypedArrayMethod,gg=Math.floor,yg=(n("reverse",function(){for(var e,t=pg(this).length,n=gg(t/2),r=0;r<n;)e=this[r],this[r++]=this[--t],this[t]=e;return this}),x.RangeError),vg=x.Int8Array,w=vg&&vg.prototype,mg=w&&w.set,bg=U.aTypedArray,l=U.exportTypedArrayMethod,wg=!d(function(){var e=new Uint8ClampedArray(2);return P(mg,e,{length:1,0:3},1),3!==e[1]}),Wn=wg&&U.NATIVE_ARRAY_BUFFER_VIEWS&&d(function(){var e=new vg(2);return e.set(1),e.set("2",1),0!==e[0]||2!==e[1]}),Eg=(l("set",function(e){bg(this);var t=Ep(1<arguments.length?arguments[1]:undefined,1),n=we(e);if(wg)return P(mg,this,n,t);var e=this.length,r=gt(n),o=0;if(e<r+t)throw new yg("Wrong length");for(;o<r;)this[t+o]=n[o++]},!wg||Wn),U.aTypedArray),Rg=U.getTypedArrayConstructor,Sg=((0,U.exportTypedArrayMethod)("slice",function(e,t){for(var n=m(Eg(this),e,t),e=Rg(this),r=0,o=n.length,i=new e(o);r<o;)i[r]=n[r++];return i},d(function(){new Int8Array(1).slice()})),Zt.some),Og=U.aTypedArray,E=((0,U.exportTypedArrayMethod)("some",function(e){return Sg(Og(this),e,1<arguments.length?arguments[1]:undefined)}),oe.match(/firefox\/(\d+)/i)),Tg=!!E&&+E[1],xg=/MSIE|Trident/.test(oe),j=oe.match(/AppleWebKit\/(\d+)\./),Ag=!!j&&+j[1],Pg=U.aTypedArray,$t=U.exportTypedArrayMethod,Ig=x.Uint16Array,Lg=Ig&&$e(Ig.prototype.sort),ze=!(!Lg||d(function(){Lg(new Ig(2),null)})&&d(function(){Lg(new Ig(2),{})})),kg=!!Lg&&!d(function(){if(ie)return ie<74;if(Tg)return Tg<67;if(xg)return!0;if(Ag)return Ag<602;for(var e,t=new Ig(516),n=Array(516),r=0;r<516;r++)e=r%4,t[r]=515-r,n[r]=r-2*e+3;for(Lg(t,function(e,t){return(e/4|0)-(t/4|0)}),r=0;r<516;r++)if(t[r]!==n[r])return!0}),Cg=($t("sort",function(e){return e!==undefined&&he(e),kg?Lg(this,e):ff(Pg(this),(n=e,function(e,t){return n!==undefined?+n(e,t)||0:t!=t?-1:e!=e?1:0===e&&0===t?0<1/e&&1/t<0?1:-1:t<e}));var n},!kg||ze),U.aTypedArray),jg=U.getTypedArrayConstructor,Ug=((0,U.exportTypedArrayMethod)("subarray",function(e,t){var n=Cg(this),r=n.length,e=dt(e,r);return new(jg(n))(n.buffer,n.byteOffset+e*n.BYTES_PER_ELEMENT,pt((t===undefined?r:dt(t,r))-e))}),x.Int8Array),Ng=U.aTypedArray,no=U.exportTypedArrayMethod,Mg=[].toLocaleString,_g=!!Ug&&d(function(){Mg.call(new Ug(1))}),n=(no("toLocaleString",function(){return _o(Mg,_g?m(Ng(this)):Ng(this),m(arguments))},d(function(){return[1,2].toLocaleString()!==new Ug([1,2]).toLocaleString()})||!d(function(){Ug.prototype.toLocaleString.call([1,2])})),U.exportTypedArrayMethod),w=x.Uint8Array,Wn=w&&w.prototype||{},Bg=[].toString,Fg=h([].join),l=(d(function(){Bg.call({})})&&(Bg=function(){return Fg(this)}),Wn.toString!==Bg),qg=(n("toString",Bg,l),r.trim),Dg=x.parseInt,E=x.Symbol,Hg=E&&E.iterator,Wg=/^[+-]?0x/i,Vg=h(Wg.exec),j=8!==Dg(u+"08")||22!==Dg(u+"0x16")||Hg&&!d(function(){Dg(Object(Hg))})?function(e,t){e=qg(O(e));return Dg(e,t>>>0||(Vg(Wg,e)?16:10))}:Dg;return C({global:!0,forced:parseInt!==j},{parseInt:j}),new Iu({name:"aid"}),Vd.use(js),Vd.use(_s),Vd.use(a),Vd.use(Gs),Vd.use(mn),Vd.use(go),Vd.use(Ws),Vd.use(p),Vd.use(o),Vd.use(e),Vd});

}</script><!--<![endif]--><script>(function(){
        if(!window.Aegis){
            return
        }
        function setCookie(name,value,domain,path,hour){if(hour){var expire = new Date();expire.setTime(expire.getTime() + 3600000 * hour);}document.cookie = name + "=" + value + "; " + (hour?("expires=" + expire.toGMTString() + "; "):"") + (path?("path=" + path + "; "):"path=/; ") + (domain?("domain=" + domain + ";"):("domain=qq.com;"));return true;};
        function getCookie(n){var r = new RegExp("(?:^|;+|\\s+)" + n + "=([^;]*)"),m = document.cookie.match(r);return (!m?"":m[1]);}
        function getJSVersion(){
            //https://pre.cdn-go.cn/qq-web/any.ptlogin2.qq.com/33d4907a
            var frags = "https://qq-web.cdn-go.cn/monorepo/9fce2a54".split('/');
            return frags[frags.length-1] || 'unknown'
        }
        
        function getUUid(){
            var clientip = getCookie("pt_clientip");
            var serverip = getCookie("pt_serverip");
            if(clientip && serverip){
                return serverip+'-'+clientip+"-"+ Math.floor(Math.random()*10000)
            }else{
                return new Date().getTime() + '-' + Math.floor(Math.random()*10000)
            }
        }
        
        function hiraishinNoJutsuShiki (seal){
            if(!seal){
                return;
            }
            var div = document.createElement('div');
            div.style.cssText='opacity: 0.005;user-select: none;position:absolute;left:0;top:0';
            div.innerText = 'hiraishinNoJutsuShiki';
            document.body && document.body.appendChild(div);
        }
        
        function isExcludeLog(log){
            if(!log||!log.level || !log.msg){
                return true
            }
            if(log.level==='32' && log.msg.indexOf('https://localhost.ptlogin2')>-1){ // 快速登录
                return true
            }
            if(log.level==='32' && log.msg.indexOf('https://localhost.sec')>-1){ //Q盾 
                return true
            }
            if(log.level==='32' && log.msg.indexOf('SCRIPT load fail:')>-1){
                //业务的脚本加载失败了，报一下
                if(log.msg.indexOf('/ptlogin/js/c_login_')>-1 || 
                log.msg.indexOf('/ptlogin/js/login_10.js')>-1 || 
                log.msg.indexOf('/fingerprintjs/index.umd.js')>-1){
                    return false;
                }
                
                // 防水墙的脚本失败了，报一下
                if(log.msg.indexOf('/TCaptcha.js')>-1 || 
                    log.msg.indexOf('https://captcha.gtimg.com/1/tcaptcha-frame')>-1 ||
                    log.msg.indexOf('https://t.captcha.qq.com/cap_union_prehandle')>-1
                    ){
                    return false;
                }
                // 二维码登录轮询失败，太过频繁，不报了
                return true
            }
            if(log.level==='4' && (log.msg.indexOf('https://qq-web.cdn-go.cn/')===-1 && log.msg.indexOf('https://pre.cdn-go.cn/')===-1)){ //只上报自己的jserror
                return true
            }


            return false
        }
        var DEFAULT_UID='10000';
        var aegisUid=DEFAULT_UID;
        var LOGIN_UIN_COOKIE_KEY = 'ptui_loginuin';
        var UID_COOKIE_KEY = '__aegis_uid';
        var offlineLog = true;
        var AEGIS_REPORT_DELAY = 100; //delay 适当的时间，可以合并上报
        try{
            aegisUid = getCookie(UID_COOKIE_KEY) ||  getCookie(LOGIN_UIN_COOKIE_KEY) || getUUid();
        }catch(err){}
            
        if(aegisUid===DEFAULT_UID){
            // 没有生成uid，不用上报了，没意义	
            return;
        }
        setCookie(UID_COOKIE_KEY, aegisUid, window.location.hostname,'/',48)
                
        if(!window.Promise){
            offlineLog = false
        }
        console.log('aegisUid:',aegisUid)
        
        if(location.href.indexOf('aegis=0')>-1){
            return
        }
        
        var aegisOptions ={
            id: 'RiaWqsnT3403yXTgVY',
            delay: AEGIS_REPORT_DELAY,  
            uin: aegisUid,
            version : getJSVersion(),
            offlineLog: offlineLog,
            reportApiSpeed: true, // 接口测速
            reportAssetSpeed: true, // 静态资源测速
            beforeReport : function(log){
                if(isExcludeLog(log)){
                    return false
                }
                return true
            }
        }
        
        console.log('aegisOptions:', aegisOptions)
        window.g_aegis = new Aegis(aegisOptions)
        window.g_aegisUid = aegisUid;
        if(window.addEventListener){
            window.addEventListener('load',function(){
                hiraishinNoJutsuShiki(aegisUid);
            })
        }else if(window.attachEvent){
            window.attachEvent('onload',function(){
                hiraishinNoJutsuShiki(aegisUid);
            })
        }
                
    })();</script> <script>(function(){
		
		var patchConsole = function(){
			var method;
			var noop = function () {};
			var methods = [
				'assert', 'clear', 'count', 'debug', 'dir', 'dirxml', 'error',
				'exception', 'group', 'groupCollapsed', 'groupEnd', 'info', 'log',
				'markTimeline', 'profile', 'profileEnd', 'table', 'time', 'timeEnd',
				'timeStamp', 'trace', 'warn'
			];
			var length = methods.length;
			var console = (window.console = window.console || {});
			while (length--) {
				method = methods[length];
				// Only stub undefined methods.
				if (!console[method]) {
					console[method] = noop;
				}
			}
		}

		var getLogPrefix = function(userDefinePrefix){
			return  new Date().getFullYear() + '-' + new Date().getMonth()  + '-' + new Date().getDate()  + ' ' + new Date().getHours() + ':' + new Date().getMinutes() + ':' + new Date().getSeconds() + '['+userDefinePrefix+']';
		}
		patchConsole()
	})()</script><script>/*==007==*/ !function(r){var n={};function o(t){var e;return(n[t]||(e=n[t]={"i":t,"l":!1,"exports":{}},r[t].call(e.exports,e,e.exports,o),e.l=!0,e)).exports}o.m=r,o.c=n,o.d=function(t,e,r){o.o(t,e)||Object.defineProperty(t,e,{"enumerable":!0,"get":r})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{"value":"Module"}),Object.defineProperty(t,"__esModule",{"value":!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{"enumerable":!0,"value":e}),2&t&&"string"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=73)}({"6":function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{"value":!0}),e.report007=e.TYPE=e.RET_CODE=e.HTTP_CODE=void 0;var n=function(){return(n=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},o=e.TYPE={"SUCCESS":0,"FAILED":1},i=e.HTTP_CODE={"OK":200,"SERVER_INTERNAL_ERROR":500,"TIMEOUT":504},c=e.RET_CODE={"OK":0,"UNKNOW":9998,"MEANINGLESS":9999,"JSON_PARSE_ERROR":9997,"EMPTY_RESPONSE_BODY":9996};e.report007=function(t){var e,r;t.cgi?(e=n({"app":"ptlogin","type":o.FAILED,"retcode":c.UNKNOW,"cost":10086},t),t.httpcode!==i.OK&&(e.retcode=c.MEANINGLESS),r="https://report.qqweb.qq.com/report/007?app="+encodeURIComponent(e.app)+"&url="+encodeURIComponent(e.cgi)+"&type="+e.type+"&httpcode="+e.httpcode+"&retcode="+e.retcode+"&cost="+e.cost,new Promise(function(t){var e=setTimeout(function(){t(1)},1e4);"complete"===document.readyState?t(0):window.addEventListener&&window.addEventListener("load",function(){clearTimeout(e),t(0)})}).then(function(){(new Image).src=r})):console.log("cgi 为空，忽略上报")}},"7":function(t,e){var n=Object.defineProperty;n&&function(){var t={};try{for(var e in n(t,"x",{"enumerable":!1,"value":t}),t)return!1;return t.x===t}catch(r){return!1}}()||(Object.defineProperty=function(t,e,r){if(n&&1==t.nodeType)return n(t,e,r);t[e]=r.value||r.get&&r.get()})},"73":function(t,e,r){r(7),r(8),t.exports=r(74)},"74":function(t,e,r){"use strict";var n=r(6);function i(e,t){var r,n=Object.keys(e);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(e),t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)),n}function o(n){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?i(Object(o),!0).forEach(function(t){var e,r;e=n,r=o[t=t],(t=function(t){t=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0===r)return("string"===e?String:Number)(t);t=r.call(t,e||"default");if("object"!=typeof t)return t;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{"value":r,"enumerable":!0,"configurable":!0,"writable":!0}):e[t]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):i(Object(o)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(o,t))})}return n}(function(){window.pt007=function(t){try{var e=o(o({},t),{"app":"ptlogin"});(0,n.report007)(e)}catch(r){}}})()},"8":function(t,e,r){"use strict";Function.prototype.bind||(Function.prototype.bind=function(t){if("function"!=typeof this)throw new TypeError("Function.prototype.bind - what is trying to be bound is not callable");var e,r=Array.prototype.slice.call(arguments,1),n=this,o=function o(){return n.apply(this instanceof e&&t?this:t,r.concat(Array.prototype.slice.call(arguments)))};return(e=function e(){}).prototype=this.prototype,o.prototype=new e,o})}}); /*==007==*/</script><script> !function(){window.onerror=function(n,e,o){var t=document.createElement("img"),_=encodeURIComponent(n+"|_|"+e+"|_|"+o+"|_|"+window.navigator.userAgent);t.src="//ui.ptlogin2.qq.com/cgi-bin/report?id=195279&msg="+_+"&v="+Math.random()}}();var g_cdn_js_fail=!1,pt={};pt.str={no_uin:"你还没有输入账号！",no_pwd:"你还没有输入密码！",no_vcode:"你还没有输入验证码！",inv_uin:"请输入正确的账号！",inv_vcode:"请输入完整的验证码！",qlogin_expire:"你所选择号码对应的QQ已经失效，请检查该号码对应的QQ是否已经被关闭。",other_login:"账号登录",h_pt_login:"密码登录",otherqq_login:"QQ账号密码登录",onekey_return:"返回扫码登录"},pt.ptui={s_url:"https\x3A\x2F\x2Fgraph.qq.com\x2Foauth2.0\x2Flogin_jump",proxy_url:"",jumpname:encodeURIComponent(""),mibao_css:encodeURIComponent(""),defaultUin:"",lockuin:parseInt("0"),href:"https\x3A\x2F\x2Fxui.ptlogin2.qq.com\x2Fcgi-bin\x2Fxlogin\x3Fappid\x3D716027609\x26daid\x3D383\x26style\x3D33\x26login_text\x3D\x25E7\x2599\x25BB\x25E5\x25BD\x2595\x26hide_title_bar\x3D1\x26hide_border\x3D1\x26target\x3Dself\x26s_url\x3Dhttps\x253A\x252F\x252Fgraph.qq.com\x252Foauth2.0\x252Flogin_jump\x26pt_3rd_aid\x3D101481973\x26pt_feedback_link\x3Dhttps\x253A\x252F\x252Fsupport.qq.com\x252Fproducts\x252F77942\x253FcustomInfo\x253D.appid101481973\x26theme\x3D2\x26verify_theme\x3D",login_sig:"",clientip:"",serverip:"",version:"202505131516",ptui_version:encodeURIComponent("25051315"),isHttps:!1,cssPath:"https://ui.ptlogin2.qq.com/style.ssl/40",domain:encodeURIComponent("qq.com"),fromStyle:parseInt(""),pt_3rd_aid:encodeURIComponent("101481973"),appid:encodeURIComponent("716027609"),lang:encodeURIComponent("2052"),style:encodeURIComponent("40"),low_login:encodeURIComponent("0"),daid:encodeURIComponent("383"),regmaster:encodeURIComponent(""),enable_qlogin:"1",noAuth:"0",target:isNaN(parseInt("0"))?{_top:1,_self:0,_parent:2}["0"]:parseInt("0"),csimc:encodeURIComponent("0"),csnum:encodeURIComponent("0"),authid:encodeURIComponent("0"),auth_mode:encodeURIComponent("0"),pt_qzone_sig:"0",pt_light:"0",pt_vcode_v1:"1",pt_ver_md5:"000D64FF6AF2E4247B21E209EB22A1DBCF002087B988CCCCD4B51233",gzipEnable:"1"}; </script><style type="text/css" id="main_css"> html{overflow:hidden}body{font-family:PingFang SC,helvetica,arial,微软雅黑,华文黑体;font-size:12px;margin:0;min-height:305px;min-width:370px}body,html{height:100%}ul{padding:0;margin:0}ul li{list-style-type:none}a,a:hover{text-decoration:none}input[type=text]:focus,input[type=password]:focus{outline:0}input::-ms-clear,input::-ms-reveal{display:none;width:0;height:0}.grayscale{-webkit-filter:grayscale(100%);-moz-filter:grayscale(100%);-ms-filter:grayscale(100%);-o-filter:grayscale(100%);filter:grayscale(100%);filter:url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale");filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1)}.uncheck{background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/checkbox_unchecked.png") no-repeat;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/checkbox_unchecked_ie.png") no-repeat\9;background-size:contain}.checked{background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/checkbox_checked.png") no-repeat;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/checkbox_checked_ie.png") no-repeat\9;background-size:contain}.login,.login_no_qlogin{margin:0 auto;border:2px solid #eee;position:relative;height:100%}.header{height:50px;width:50px;position:absolute;top:0;right:0;font-family:"微软雅黑";z-index:1000}.header .switch{height:45px;position:absolute;left:60px;bottom:0;font-size:16px}.header .switch #switcher_qlogin{margin-right:85px}.header .switch .switch_btn{color:#999;display:inline-block;height:45px;line-height:45px;outline:0}.header .switch .switch_btn_focus{color:#333;display:inline-block;height:45px;line-height:45px;outline:0}.error_tips,.loading_tips,.login_no_qlogin .header .switch .switch_bottom,.login_no_qlogin .header .switch .switch_btn{display:none}.header .switch .switch_btn:hover{color:#333;text-decoration:none}.header .switch .switch_btn_focus:hover{text-decoration:none}.header .switch .switch_bottom{position:absolute;bottom:-1px;border-bottom:2px solid #848484}.header .close{width:47px;height:20px;float:right;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) -225px -126px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) -225px -126px no-repeat;outline:0}.login_no_qlogin .header{border:none;height:40px}.login_no_qlogin .header .switch{height:40px;position:absolute;left:20px;top:10px;font-size:22px}.login_no_qlogin .header .switch .switch_btn_focus{color:#333;cursor:default;text-decoration:none;display:inline-block;height:30px;line-height:30px}.web_login{position:relative;height:100%;top:50%;margin-top:-166px}.web_login .tips{position:relative;margin:0 auto 26px;z-index:11;font-size:12px;line-height:16px;color:#000;text-align:center}.error_tips{position:absolute;width:250px;left:50%;margin-left:-125px;z-index:1000;top:50%}.error_tips .error_logo{position:absolute;height:16px;line-height:16px;width:16px;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/error_icon.png") no-repeat;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/error_icon_ie.png") no-repeat\9;background-size:contain}.loading_tips{text-align:center;height:24px;position:absolute;top:10px;left:50%;margin-left:-28px;z-index:1000}.error_tips .err_m{display:inline-block;padding-left:22px;line-height:16px;color:#FF5765;vertical-align:middle;text-align:left}.web_login .operate_tips{display:none;padding:8px;border:1px solid #dad699;background:#f9f5c7;position:absolute;bottom:-3px;border-radius:2px}.web_login .operate_tips .tips_link{color:#f39800}.web_login .operate_tips .down_row{position:absolute;bottom:-6px;_bottom:-7px;left:20px;width:12px;height:6px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) -162px -161px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) -162px -161px no-repeat;font-size:1px}.web_login .operate_tips .operate_content{width:100%;text-align:left}.web_login .login_form{width:250px;margin:0 auto}.web_login .inputOuter,.web_login .inputOuter_focus{width:250px;height:38px}.web_login .inputstyle{width:232px;position:relative;height:16px;padding:10px 0 10px 16px;line-height:16px;border:1px solid #CCC;border-radius:4px;background:0 0;color:#000;font-family:PingFang SC;font-size:13px}.web_login .inputstyle:-webkit-autofill{box-shadow:0 0 0 1000px #FFF inset}.web_login .input_tips,.web_login .input_tips_focus{position:absolute;top:10px;+top:10px;left:16px;font-size:13px;line-height:18px;color:#CCC;cursor:text}.web_login .input_tips_focus{color:#CCC}.web_login .uinArea{height:48px;position:relative;z-index:10}.web_login .uin_del{width:21px;height:21px;cursor:pointer;position:absolute;right:16px;top:8px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) -116px -160px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) -116px -160px no-repeat;display:none}.web_login .uin_del:hover{background-position:-139px -160px}.web_login .email_list{border:1px solid #70c2ec;background:#fff;width:266px;position:absolute;z-index:10;display:none;border-radius:1px;padding:1px;left:2px}.web_login .email_list li,.web_login .email_list p{height:31px;line-height:31px;margin:0;overflow:hidden;padding-left:10px}.web_login .email_list p{height:16px;line-height:10px}.web_login .email_list .hover{background:#cbe2fa}.web_login .pwdArea{height:68px;position:relative;z-index:3}.web_login .lock_tips{position:absolute;top:32px;left:-15px;height:16px;padding:5px;text-align:center;display:none;border:1px solid #dad699;background:#f9f5c7;border-radius:2px}.web_login .lock_tips .lock_tips_row{position:absolute;top:-6px;left:25px;width:12px;height:6px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) -162px -168px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) -162px -168px no-repeat}.web_login .verifyArea{display:none;height:120px;position:relative}.web_login .verifyinputArea{height:55px}.web_login .verifycode{color:#333;font-size:16px}.web_login .verifyimgArea{position:relative;height:55px;cursor:pointer}.web_login .verifyimgArea .verifyimg{height:55px;width:150px;position:absolute;left:0}.web_login .verifyimgArea .verifyimg_tips{position:absolute;left:165px;top:15px;color:#000}.web_login .submit{position:relative;height:38px;border-radius:4px;background-color:#09F}.web_login .login_button{position:absolute;left:0;outline:0;width:100%}.web_login .login_button .btn{width:100%;height:38px;line-height:18px;border:none;font-size:14px;font-weight:400;color:#fff;background:0 0;cursor:pointer}.web_login .login_button:hover .btn{background-position:-116px -90px}.web_login .login_button:hover{text-decoration:none}.web_login .low_login{margin-top:6px}.web_login .low_login .checked,.web_login .low_login .uncheck{float:left;height:16px;width:16px}.web_login .low_login .low_login_wording{height:16px;line-height:16px;line-height:16px\9;cursor:pointer;margin-left:6px}.bottom{height:16px;width:100%;position:absolute;bottom:32px;text-align:center;font-size:12px;color:#000;line-height:16px}.bottom .bottom_menu{display:flex;align-items:center;justify-content:space-between}.bottom .link{color:#000;vertical-align:middle}.bottom .dotted{display:inline-block;height:12px;margin:0 22px;border-left:1px solid #F5F5F5;vertical-align:middle;color:transparent}.bottom .vip_link:hover{color:red}.bottom .low_login{position:absolute;bottom:25px;left:0;right:0;width:fit-content;margin:0 auto}.bottom .low_login .checked,.bottom .low_login .uncheck{float:left;height:16px;width:16px}.bottom .low_login .low_login_wording{height:16px;line-height:16px;line-height:16px\9;cursor:pointer;margin-left:6px}.web_qr_login{position:relative;overflow:hidden;height:100%}.web_qr_login .web_qr_login_show{top:0;height:100%}.noscript{background:#F9F5C7;border:1px solid #DAD699;display:inline-block;height:24px;line-height:24px;padding:5px;text-align:center}.authLogin,.newVcodeArea{background:#FFF;z-index:9999}.authLogin,.hide{display:none}.authLogin{height:325px;position:absolute;top:0;width:100%}.authLogin .authHeader{height:50px;border-bottom:1px solid #e2e2e2;position:relative;font-family:"Hiragino Sans GB","微软雅黑"}.authLogin .authHeader .title{position:absolute;top:5px;left:20px;line-height:30px;height:30px;font-size:22px}.authLogin .authTips{height:30px}.authLogin .authInfo{text-align:center}.authLogin .authWording{text-align:center;color:#A0A0A0;line-height:20px;height:35px}.authLogin .face{display:inline-block;height:120px;width:120px;text-align:center;position:relative;cursor:pointer;outline:0}.authLogin .face:focus{outline:0}.authLogin .face:hover{border:none;text-decoration:none}.authLogin .face img{width:80px;height:80px;position:absolute;top:10px;left:19px;border:none}.authLogin .face .img_out{width:88px;height:88px;position:absolute;top:4px;left:14px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) 0 -182px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) 0 -182px no-repeat}.authLogin .face .img_out_focus,.authLogin .face:hover .img_out{width:88px;height:88px;position:absolute;top:5px;left:15px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) -91px -183px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) -91px -183px no-repeat}.authLogin .face .nick{display:inline-block;text-align:center;position:absolute;top:100px;left:0;height:20px;line-height:18px;vertical-align:middle;width:100%;overflow:hidden;color:#6f7479}.authLogin .red{color:red}.authLogin .face .vip_logo{width:26px;height:12px;position:absolute;top:12px;left:21px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) -236px -92px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) -236px -92px no-repeat}.authLogin .face .uin{display:none}.authLogin .face:hover .uin{display:block;background:#000;height:20px;width:80px;line-height:20px;position:absolute;left:20px;top:72px;filter:Alpha(opacity=50);opacity:.5;color:#fff;border-radius:0 0 4px 4px}.authLogin .face .face_mengban{background:#000;width:80px;height:80px;position:absolute;top:10px;left:20px;filter:Alpha(opacity=30);opacity:.3}.authLogin .cancleAuthOuter{margin-top:10px;text-align:center}.authLogin .cancleAuth{display:inline-block;height:14px;border:1px solid #d3d3d3;padding:3px;cursor:pointer;color:#888;text-decoration:none}.authLogin .bottom{position:absolute;bottom:-2px;left:0;width:100%;height:25px}.authLogin .low_login{position:absolute;bottom:0;left:10px}.authLogin .low_login .checked,.authLogin .low_login .uncheck{float:left;height:16px;width:16px}.authLogin .low_login .low_login_wording{height:16px;line-height:16px;line-height:16px\9;cursor:pointer;margin-left:6px}.authLogin .feedback_authLogin{position:absolute;bottom:0;right:10px}.qlogin{height:50%;top:50%;position:absolute;margin:-166px auto 0;display:none;width:100%}.qlogin .nextPage,.qlogin .prePage{top:82px;text-align:center;position:absolute;display:none}.qlogin .qlogin_tips{color:#000;font-size:12px;line-height:16px;text-align:center;position:relative;margin-bottom:16px;zoom:1}.qlogin .qlogin_select{height:160px;position:relative}.qlogin .qlogin_show{height:210px;margin-left:30px;margin-right:30px;overflow:hidden;position:relative}.qlogin .qlogin_list{text-align:center;position:relative}.qlogin .prePage{width:12px;height:120px;left:15px}.qlogin .nextPage{width:12px;height:120px;right:15px}.qlogin .nextRow,.qlogin .preRow{height:21px;display:inline-block}.qlogin .preRow{width:12px;margin-top:43px;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/go_left.png") no-repeat;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/go_left_ie.png") no-repeat\9;background-size:contain;cursor:pointer}.qlogin .nextRow{width:12px;margin-top:43px;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/go_right.png") no-repeat;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/go_right_ie.png") no-repeat\9;background-size:contain;cursor:pointer}.qlogin .face{display:inline-block;height:120px;width:105px;text-align:center;position:relative;cursor:pointer;outline:0}.qlogin .face:focus{outline:0}.qlogin .face:hover{border:none;text-decoration:none}.qlogin .face img{width:85px;height:85px;position:absolute;top:10px;left:10px;border:none}.qlogin .face .img_out,.qlogin .face .img_out_focus,.qlogin .face:hover .img_out{width:85px;height:85px;position:absolute;left:9px;top:9px}.qlogin .face .img_out,.qlogin .face .img_out_focus{background:0 0;border:1px solid #F5F5F5;border-radius:2px}.qlogin .face .nick,.qlogin_list .return{display:inline-block;text-align:center;left:0;font-size:12px;line-height:16px;width:100%;height:16px;position:absolute;vertical-align:middle;overflow:hidden}.qlogin .face .nick{top:101px;color:#000}.qlogin_list .return{top:120px;columns:#000}.bottom .red{color:red}.qlogin .face .red{color:#000}.qlogin .face .vip_logo{width:28.67px;height:16px;position:absolute;top:10px;left:10px;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/vip.png") no-repeat;background-size:contain;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/vip_ie.png") no-repeat\9}.qlogin .face .onekey_logo{width:24px;height:24px;position:absolute;bottom:25px;right:7px;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/phone.png") no-repeat;background-size:contain;background:url("https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/40/images/phone_ie.png") no-repeat\9}.qlogin .face .uin,.qlogin .face .uin_menban{display:none}.qlogin .face:hover .uin,.qlogin .face:hover .uin_menban{display:block;height:20px;line-height:20px;top:76px;position:absolute;width:85px;left:10px}.qlogin .face:hover .uin_menban{background:#000;filter:Alpha(opacity=50);opacity:.5;border-radius:0 0 2px 2px}.qlogin .face:hover .uin{color:#fff}.qlogin .face .face_mengban{background:#000;width:85px;height:85px;position:absolute;top:10px;left:9px;filter:Alpha(opacity=30);opacity:.3}.qlogin .tips{height:60px;margin-top:20px}.qlogin .tips_logo{display:none}.qlogin .err_m{display:inline-block;height:18px;vertical-align:middle;line-height:18px;color:#a0a0a0}.qlogin .loading_tips,.qlogin .wording_tips{height:25px;text-align:center}.qlogin .loading_tips{visibility:hidden}.qlogin .low_login{position:absolute;top:213px;left:0;right:0;margin:0 auto;width:fit-content}.qlogin .low_login .checked,.qlogin .low_login .uncheck{float:left;height:16px;width:16px}.qlogin .low_login .low_login_wording{float:left;height:16px;line-height:16px;line-height:16px\9;margin-left:6px;color:#000;cursor:pointer}.qlogin .qr_0{display:inline-block;height:100%;min-height:120px;width:105px;text-align:center;position:relative}.qlogin .qr_0 a{outline:0}.qlogin .qr_0 .qr_safe_login,.qlogin .qr_0 .qr_short_tips{display:none}.qlogin .qr_0 .qr_info_link{background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) 0 -541px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) 0 -541px no-repeat;color:grey;display:inline-block;height:30px;line-height:30px;text-decoration:none;width:152px}.qlogin .qr_0 .qr_info_link:hover{background-position:0 -573px;text-decoration:none}.qlogin .qr_0 .qr_info_link_en{text-decoration:none;display:inline-block;width:152px;height:38px;line-height:18px;color:grey;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) 0 -606px no-repeat;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) 0 -606px no-repeat}.qlogin .qr_0 .qr_info_link_en:hover{background-position:0 -647px;text-decoration:none}.qlogin .qr_0 .qr_safe_tips{height:20px;line-height:20px;font-size:14px;font-weight:700;text-align:center;margin-left:5px}.qlogin .qr_0 .qrImg{position:absolute;height:85px;width:85px;top:9px;left:9px;border:1px solid #F5F5F5;z-index:1000}.qlogin .qr_0 .qr_app_name{display:inline-block;text-align:center;position:absolute;bottom:0;left:4px;vertical-align:middle;width:100%;overflow:hidden}.qlogin .qr_0 .qr_mengban,.qlogin .qr_0 .qrlogin_img_out{background:rgba(0,0,0,.4);#background:none;-ms-filter:progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr='#66000000', EndColorStr='#66000000');filter:progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr='#66000000', EndColorStr='#66000000');zoom:1;height:85px;left:10px;opacity:.5;position:absolute;top:10px;width:85px;z-index:1000}.qlogin .qr_0 .qrlogin_img_out,:root .qlogin .qr_0 .qr_mengban{filter:none\9}.qlogin .qr_0 .qrlogin_img_out{background:0 0;-ms-filter:none;filter:none}.qlogin .qr_0 .qr_invalid_tips{color:#FFF;cursor:pointer;left:10px;line-height:20px;position:absolute;text-align:center;top:32px;width:85px;z-index:1000}.qlogin .qr_1{display:inline-block;height:120px;width:105px;text-align:center;position:relative}.qlogin .qr_1 .qr_info_link,.qlogin .qr_1 .qr_info_link_en{display:none}.qlogin .qr_1 .qrImg{height:85px;position:absolute;top:9px;left:9px;width:85px;border-radius:2px;border:1px solid #F5F5F5}.qlogin .qr_1 .qr_app_name{display:inline-block;text-align:center;position:absolute;top:100px;left:0;height:20px;line-height:18px;vertical-align:middle;width:108px;overflow:hidden}.qlogin .qr_1 .qr_app_name a{cursor:pointer;outline:0}.qlogin .qr_short_tips{color:#3481CF}.qlogin .qr_1 .qr_tips{position:absolute;top:30;left:0;width:100px;height:150px;display:none}.qlogin .qr_invalid{display:none}.qlogin .qr_1 .qr_mengban,.qlogin .qr_1 .qrlogin_img_out{background:rgba(0,0,0,.4);#background:none;-ms-filter:progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr='#66000000', EndColorStr='#66000000');filter:progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr='#66000000', EndColorStr='#66000000');zoom:1;height:85px;left:10px;opacity:.4;position:absolute;top:10px;width:85px;z-index:1000}.qlogin .qr_1 .qrlogin_img_out,:root .qlogin .qr_1 .qr_mengban{filter:none\9}.qlogin .qr_1 .qrlogin_img_out{background:0 0;-ms-filter:none;filter:none}.qlogin .qr_1 .qr_invalid_tips{color:#FFF;cursor:pointer;left:10px;line-height:20px;position:absolute;text-align:center;top:32px;width:85px;z-index:1000}.onekey_step2,.qrlogin_step2{width:100%;left:0;z-index:1000;top:0;display:none}.qlogin .qr_1 .qr_safe_tips{display:none}.qrlogin_step2{position:absolute;height:100%;background:#fff}.qrlogin_step2 .step2_outer{height:110px;margin:50px 0 0 33px;padding:80px 0 0 115px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_tiny.png) 0 -348px no-repeat #fff;_background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/icon_3_8.png) 0 -348px no-repeat #fff}.qrlogin_step2 .qr_h3{text-align:left;margin:0;padding-bottom:20px;font-size:14px;font-weight:700}.qrlogin_step2 .goBack{color:#b4b4b4}.qrlogin_step2 .goBack a{color:#3481cf}.onekey_step2{background:#fff;height:100%;text-align:center;position:absolute}.onekey_step2 .outer,.qr_tips{left:50%;top:50%;position:absolute}.onekey_step2 .outer{margin-top:-120px;width:330px;margin-left:-165px}.onekey_step2 .container{height:198px;padding-left:115px;background:url(//imgcache.qq.com/ptlogin/v4/style/40/images/onekey_tips.png) no-repeat #fff;text-align:left}.bottom.center,.docsqq .bottom,.guanjia,.title{text-align:center}.onekey_step2 .container .prompt{font-size:16px;margin-bottom:12px}.onekey_step2 .container .operate{font-size:12px;margin-bottom:10px}.onekey_step2 .container .operate a{margin-right:20px}#onekey_tips{color:#FF5765}.qr_tips{display:none;width:92px;height:172px;z-index:1999;margin-top:-64px}.qr_tips .qr_tips_menban{opacity:.5;filter:alpha(opacity=50);background:#fff;width:100%;height:100%}.qr_tips .qr_tips_pic{width:92px;height:172px;position:absolute;top:0}.qr_tips .qr_tips_pic_chs,.qr_tips .qr_tips_pic_cht,.qr_tips .qr_tips_pic_en{background:url(https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/20/images/shouQ_v2/qr_tips.png) no-repeat;background-size:100%}@media screen and (-webkit-min-device-pixel-ratio:2),screen and (min--moz-device-pixel-ratio:2){.qr_tips .qr_tips_pic_chs,.qr_tips .qr_tips_pic_cht,.qr_tips .qr_tips_pic_en{background:url(https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/20/images/shouQ_v2/<EMAIL>) no-repeat;background-size:100%}}.newVcodeArea{height:315px;position:absolute;top:0;width:100%;display:none}#onekey_verify{width:289px;color:#2f3436;margin:12px auto 0;letter-spacing:1px;display:none}.ov-tips{letter-spacing:1px;height:35px;display:none}.ov-tips em{font-size:14px;font-weight:700;font-style:normal;letter-spacing:0}.ov-scene{width:100%;height:137px;margin-top:18px}.ov-1 .aq-app,.ov-2 .aq-wx{display:block}.invalid .aq-app,.invalid .aq-wx{display:none}.invalid .timeout{display:block;color:red}.ov-1 .ov-scene{background:url("/style/20/images/aq/mail_verify_1.png") 39px 0 no-repeat}.ov-2 .ov-scene{background:url("/style/20/images/aq/mail_verify_2.png") 39px 0 no-repeat}.invalid .ov-scene{background-position:39px -150px}.link_tips,.timeout a{color:#2E77E5}.title{font-size:20px;line-height:28px;color:#000;margin:16px 0 6px}.guanjia{position:relative}.web_login .guanjia{top:10px}.qlogin .guanjia{top:-61px}.bottom.center{right:0}.guanjia_checkbox,.guanjia_logo{vertical-align:bottom;margin:0;padding:0}.guanjia_tips{font-size:12px;color:#666;padding-left:3px}.docsqq{border:none;height:100%!important;font-family:PingFang SC,Microsoft YaHei,-apple-system-font,\\9ED1\4F53,Helvetica Neue,Helvetica,STHeiTi,sans-serif}.docsqq .title{font-size:28px;line-height:28px;color:#000}.docsqq .bottom{right:0}.docsqq .docs-dotted{display:none} .ptui-dialog-wrap {
    position:fixed;
    top:0;
    left:0;
    width:100%;
    height:100%;
    z-index:10000;
}
.ptui-dialog-wrap__inner {
    position:absolute;
    top:50%;
    left:50%;
    z-index:10002;
    width:360px;
    transform:translate(-50%, -50%);
    background-color:#FFF;
    border-radius:6px;
    overflow:hidden;
}
.ptui-dialog-wrap-mail__inner {
    position:absolute;
    top:50%;
    left:50%;
    z-index:10002;
    width:320px;
    transform:translate(-50%, -50%);
    background-color:#FFF;
    border-radius:6px;
    overflow:hidden;
}
.ptui-dialog-wrap__hd {
    display:flex;
    flex-direction:row;
    justify-content:space-between;
    padding:4px;
}
.ptui-dialog-wrap__btn-close {
    display:block;
    width:16px;
    height:16px;
		padding:0;
		margin:0;
    background-color:transparent;
    cursor: pointer;
		border:0 none;
}
.ptui-dialog-wrap__icon {
    display:block;
    width:16px;
    height:16px;
}
.ptui-dialog-wrap__text-title {
    font-size: 13px;
    font-weight: 500;
    line-height:1.5;
    color: #000000;

    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.ptui-dialog-wrap__text-title + .ptui-dialog-wrap__text-content {
    margin-top:8px;
}
.ptui-dialog-wrap__text-link {
    color:#2E77E5;
}
.ptui-dialog-wrap__bd {
    padding:10px 20px 10px 20px;
}
.ptui-dialog-wrap__text-content {
    display:inline-block;
    line-height:1.5;
    font-size:12px;
    color:#000000;
}
.ptui-dialog-wrap__text-content1 {
    display:block;
    line-height:1.5;
    font-size:14px;
    color:rgba(0, 0, 0, 0.6);
	font-weight: 400;
	margin-bottom: 10px;
}
.ptui-dialog-wrap__text-content2 {
    display:block;
    line-height:1.5;
    font-size:14px;
	font-weight: 400;
    color:rgba(0, 0, 0, 0.6);
}
.ptui-dialog-wrap__text-content2__special {
    color: #000;
}
.ptui-dialog-wrap__text-content3 {
    display:block;
    font-size:14px;
	font-weight: 400;
    color:rgba(0, 0, 0, 0.6);
}

.ptui-dialog-wrap__ftmail {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.ptui-dialog-wrap__ftmail-qrcode {
    width: 90px;
    height: 90px;
    background-image: url("https://static-res.qq.com/static-res/ptlogin/kefu-qrcode.jpg");
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0 8px 10px 20px;
}
.ptui-dialog-wrap__ftmail-title {
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 22px; 
}
.ptui-dialog-wrap__ft {
    padding: 10px 20px 20px 0;

    display:flex;
    flex-direction:row;
    justify-content:flex-end;
}
.ptui-dialog-wrap__btn {
    margin-right:10px;
		padding:3px 19px;
		max-height:28px;
		line-height:1.5;
    text-align: center;
    font-size: 13px;
    font-weight: 400;
    box-sizing: border-box;
    outline:0 transparent;
    border-radius:4px;
    cursor: pointer;
}
.ptui-dialog-wrap__btn:nth-last-of-type(1) {
    margin-right:0;
}
.ptui-dialog-wrap__btn-confirm {
    color:#FFF;
    background-color:#0099FF;
    border:1px solid transparent;
}
.ptui-dialog-wrap__btn-cancel {
    border:1px solid #CCCCCC;
    color:#000000;
    background-color:transparent;
}
.ptui-dialog-wrap__mask {
    position:absolute;
    top:0;
    left:0;
    z-index:8;
    width:100%;
    height:100%;
    background-color:rgba(0, 0, 0, 0.32);
}

	.login, .login_no_qlogin{   border:0px;  background-color:#ffffff; }
	.header .logo,.authHeader .logo{
		background:url("https://ui.ptlogin2.qq.com/style/11/images/icon_24_c_3.png") no-repeat;
		_background:url("https://ui.ptlogin2.qq.com/style/11/images/icon_3.png") no-repeat;  background-position:0 -280px;  }  /* 搜狗客户端 */  </style>  <link href="https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/v4/style/theme/theme_2.css" rel="stylesheet" type="text/css"> <script type="text/javascript" charset="utf-8" crossorigin="anonymous" src="https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/js/c_login_2.js"></script><script src="https://localhost.ptlogin2.qq.com:4301/pc_querystatus?callback=ptui_pc_querystatus_CB&amp;appid=ptlogin&amp;subappid=101481973&amp;r=0.7379598800413935&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.sec.qq.com:9410/?cmd=101&amp;service=1&amp;action=undefined&amp;timeout=5000&amp;_tk=0.9394916943048354&amp;encrypt=0&amp;_ts=1751692095329&amp;callback=pt_qqprotect_version&amp;wparam=&amp;lparam=&amp;session="></script><script src="https://localhost.ptlogin2.qq.com:4301/pt_get_uins?callback=ptui_getuins_CB&amp;r=0.5195636495087594&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.ptlogin2.qq.com:4303/pc_querystatus?callback=ptui_pc_querystatus_CB&amp;appid=ptlogin&amp;subappid=101481973&amp;r=0.7379598800413935&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.ptlogin2.qq.com:4305/pc_querystatus?callback=ptui_pc_querystatus_CB&amp;appid=ptlogin&amp;subappid=101481973&amp;r=0.7379598800413935&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.sec.qq.com:16873/?cmd=101&amp;service=1&amp;action=undefined&amp;timeout=5000&amp;_tk=0.9394916943048354&amp;encrypt=0&amp;_ts=1751692095329&amp;callback=pt_qqprotect_version&amp;wparam=&amp;lparam=&amp;session="></script><script src="https://localhost.ptlogin2.qq.com:4307/pc_querystatus?callback=ptui_pc_querystatus_CB&amp;appid=ptlogin&amp;subappid=101481973&amp;r=0.7379598800413935&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.ptlogin2.qq.com:4303/pt_get_uins?callback=ptui_getuins_CB&amp;r=0.5195636495087594&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.ptlogin2.qq.com:4309/pc_querystatus?callback=ptui_pc_querystatus_CB&amp;appid=ptlogin&amp;subappid=101481973&amp;r=0.7379598800413935&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.sec.qq.com:9410/?cmd=101&amp;service=104&amp;action=3&amp;timeout=5000&amp;_tk=0.9394916943048354&amp;encrypt=0&amp;_ts=1751692095843&amp;callback=ptui_qqprotect_querystatus_CB&amp;wparam=%7B%22appid%22%3A%22ptlogin%22%2C%22subappid%22%3A%22101481973%22%2C%22qqnum%22%3A%22123456%22%2C%22msgid%22%3A1%7D&amp;lparam=&amp;session="></script><script src="https://localhost.ptlogin2.qq.com:4305/pt_get_uins?callback=ptui_getuins_CB&amp;r=0.5195636495087594&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.sec.qq.com:16873/?cmd=101&amp;service=104&amp;action=3&amp;timeout=5000&amp;_tk=0.9394916943048354&amp;encrypt=0&amp;_ts=1751692095843&amp;callback=ptui_qqprotect_querystatus_CB&amp;wparam=%7B%22appid%22%3A%22ptlogin%22%2C%22subappid%22%3A%22101481973%22%2C%22qqnum%22%3A%22123456%22%2C%22msgid%22%3A1%7D&amp;lparam=&amp;session="></script><script src="https://localhost.ptlogin2.qq.com:4307/pt_get_uins?callback=ptui_getuins_CB&amp;r=0.5195636495087594&amp;pt_local_tk=-1382752229"></script><script src="https://localhost.ptlogin2.qq.com:4309/pt_get_uins?callback=ptui_getuins_CB&amp;r=0.5195636495087594&amp;pt_local_tk=-1382752229"></script></head>  <body><div class="login" id="login" style="height: 331px;"><div class="header">  </div><div class="error_tips" id="error_tips" style="display: none;"><span class="error_logo" id="error_logo"></span> <span class="err_m" id="err_m"></span></div><div class="loading_tips" id="loading_tips"><span id="loading_wording">登录中</span><img id="loading_img" place_src="https://ui.ptlogin2.qq.com/style/0/images/load.gif" align="absmiddle" src="https://ui.ptlogin2.qq.com/style/0/images/load.gif"></div><div class="qlogin" id="qlogin" style="display: block;"><div class="title" id="title_0" style="display: block;">快捷登录</div><div class="title" id="title_1" style="display: none;">安全验证</div><div class="qlogin_tips" id="qlogin_tips_0" style="display: block;">使用<a class="link_tips" href="http://im.qq.com/mobileqq/#from=login" target="_blank">QQ手机版</a>扫码登录。</div><div class="qlogin_tips" id="qlogin_tips_1" style="display: none;">使用<a class="link_tips" href="http://im.qq.com/mobileqq/#from=login" target="_blank">QQ手机版</a>扫码登录，或点击头像授权登录。</div><div class="qlogin_tips" id="qlogin_tips_2" style="display: none;">登录环境异常（异地登录或IP存在风险）<br>请使用<a class="link_tips" href="http://im.qq.com/mobileqq/#from=login" target="_blank">QQ手机版</a>扫码登录，保护账号安全。</div><div class="qlogin_tips" id="qlogin_tips_3" style="display: none;">你已开启了设备锁安全登录服务，<br>请使用<a class="link_tips" href="http://im.qq.com/mobileqq/#from=login" target="_blank">QQ手机版</a>扫码进行登录。</div><div class="qlogin_tips" id="qlogin_tips_4" style="display: none;">请在<a class="link_tips" href="http://im.qq.com/mobileqq/#from=login" target="_blank">QQ手机版</a>上确认使用此账号登录</div><a title="上一页" class="prePage" id="prePage" href="javascript:void(0);" style="display: none;"><span class="preRow" tabindex="6"></span></a><div class="qlogin_show" id="qlogin_show" style="width: 315px; left: 50%; margin-left: -157.5px;"><div class="qlogin_list" id="qlogin_list" style="width: 315px;"><span id="qr_area" class="qr_0"><img id="qrlogin_img" class="qrImg" alt="" src="https://xui.ptlogin2.qq.com/ssl/ptqrshow?appid=716027609&amp;e=2&amp;l=M&amp;s=3&amp;d=72&amp;v=4&amp;t=0.8850995917006267&amp;daid=383&amp;pt_3rd_aid=101481973&amp;u1=https%3A%2F%2Fgraph.qq.com%2Foauth2.0%2Flogin_jump" style="position: absolute; left: 9px;"> <span class="qrlogin_img_out" onmouseover="pt.plogin.showQrTips();" onmouseout="pt.plogin.hideQrTips();"></span> <span id="qr_invalid" class="qr_invalid" onclick="pt.plogin.begin_qrlogin();" style="display: none;"><span id="qr_mengban" class="qr_mengban"></span> <span id="qr_invalid_tips" class="qr_invalid_tips"> 二维码失效 <br> 请点击刷新 </span></span></span></div></div><div class="guanjia hide"><img class="guanjia_logo hide" src="//imgcache.qq.com/ptlogin/v4/style/40/images/logo.png"><input type="checkbox" class="guanjia_checkbox hide"><span class="guanjia_tips"></span></div><a title="下一页" class="nextPage" id="nextPage" href="javascript:void(0);" style="display: none;"><span class="nextRow" tabindex="7"></span></a></div><div class="web_qr_login" id="web_qr_login" style="display: none;"><div class="web_qr_login_show" id="web_qr_login_show"><div class="web_login" id="web_login"><div class="tips" id="tips">  <noscript id="noscript_area"><span class="noscript">你的浏览器脚本被禁用了， <a href="/assistant/noscript.html" target="_blank" style="color: #29B1F1">查看启用方法</a> </span><img id="noscript_img" style="width:1px;height:1px;" src="https://ui.ptlogin2.qq.com/cgi-bin/report?id=301240"></noscript>  <div class="title" id="title_2">密码登录</div><div id="qlogin_entry">推荐使用<a class="switch_btn_focus link_tips" hidefocus="true" id="switcher_qlogin" href="javascript:void(0);" tabindex="7">快捷登录</a>，防止盗号。</div><div class="operate_tips" id="operate_tips"><span class="operate_content">手机号码也可登录哦， <a class="tips_link" id="bind_account" href="javascript:void(0);">登录个人中心绑定</a> </span><span class="down_row"></span></div></div><div></div><div class="login_form"><form id="loginform" autocomplete="off" name="loginform" action="" method="post" target="0" style="margin:0px;"><div class="uinArea" id="uinArea"><label class="input_tips" id="uin_tips" for="u" data-onlyqq="QQ号码">支持QQ号/邮箱/手机号登录</label><div class="inputOuter"><input type="text" class="inputstyle" id="u" name="u" value="" tabindex="1"> <a class="uin_del" id="uin_del" href="javascript:void(0);"></a></div><ul class="email_list" id="email_list"></ul></div><div class="pwdArea" id="pwdArea"><label class="input_tips" id="pwd_tips" for="p">请输入密码</label><div class="inputOuter" id="pwdAreaInputOuter"><input type="password" class="inputstyle password" id="p" name="p" value="" maxlength="16" tabindex="2"></div><div class="lock_tips" id="caps_lock_tips"><span class="lock_tips_row"></span> <span>大写锁定已打开</span></div></div><div class="verifyArea" id="verifyArea"><div class="verifyinputArea" id="verifyinputArea"><label class="input_tips" id="vc_tips" for="verifycode">验证码</label><div class="inputOuter"><input name="verifycode" type="text" class="inputstyle verifycode" id="verifycode" value="" tabindex="3"></div></div><div class="verifyimgArea" id="verifyimgArea"><img class="verifyimg" id="verifyimg" title="看不清，换一张"> <a tabindex="4" href="javascript:void(0);" class="verifyimg_tips">看不清，换一张</a></div></div><div class="submit" style="top: 0px;"><a class="login_button" href="javascript:void(0);" hidefocus="true"><input type="submit" tabindex="6" value="登录" class="btn" id="login_button"></a></div></form></div><div class="guanjia hide"><img class="guanjia_logo hide" src="//imgcache.qq.com/ptlogin/v4/style/40/images/logo.png"><input type="checkbox" class="guanjia_checkbox hide"><span class="guanjia_tips"></span></div></div><div class="bottom" id="bottom_web"><a href="https://ssl.ptlogin2.qq.com/ptui_forgetpwd" class="link" id="forgetpwd" target="_blank">找回密码</a>  <span class="dotted"></span> <a href="https://ssl.ptlogin2.qq.com/j_newreg_url" class="link" target="_blank">注册账号</a>  <span class="dotted"></span> <a class="link" id="feedback_web" href="https://support.qq.com/products/77942?customInfo=.appid101481973" target="_blank">意见反馈</a>  </div></div></div><div id="qrlogin_step2" class="qrlogin_step2"><div class="step2_outer"><div class="qr_h3">扫描成功，请在手机上确认是否授权登录</div><div class="goBack"> 使用其它方式登录，请 <a id="goBack" href="javascript:void(0);" tabindex="14">返回</a></div></div></div><div id="qrlogin_step3" class="qrlogin_step2"><div class="step2_outer"><div class="qr_h3">验证成功，正在登录中，请稍后。</div><div class="goBack"> 如10s内未自动跳转，请 <a id="goBackLoading" href="javascript:void(0);" tabindex="14">返回</a></div></div></div><div id="onekey_step2" class="onekey_step2"><div class="outer"><div class="title">QQ手机版授权</div><div class="container"><div class="prompt">请打开<a class="link_tips" href="http://im.qq.com/mobileqq/#from=login" target="_blank">QQ手机版</a>，确认登录</div><div class="operate"> 没有收到登录确认？请<a href="javascript:pt.qlogin.onekeyPush(pt.qlogin.__onekeyUin)">重新发送。</a></div><div class="operate"> 使用扫码登录/密码登录，请&nbsp;<a style="text-decoration: underline;" href="javascript:pt.plogin.go_onekey_step(1)">返回</a></div><div id="onekey_tips"></div></div></div><div class="bottom"><a class="link" href="https://support.qq.com/products/77942?customInfo=.appid101481973" target="_blank">意见反馈</a></div></div><div id="qr_tips" class="qr_tips" style="display: none; left: 62.1667px;"><div class="" id="qr_tips_menban"></div><div class="qr_tips_pic qr_tips_pic_chs" id="qr_tips_pic" style="opacity: 0;"></div></div><div class="bottom hide" id="bottom_qlogin" style="display: block;"><a class="link" hidefocus="true" id="switcher_plogin" href="javascript:void(0);" tabindex="8">密码登录</a>  <span class="dotted" id="docs_dotted"></span> <a href="https://ssl.ptlogin2.qq.com/j_newreg_url" class="link" target="_blank">注册账号</a>   <span class="dotted"></span> <a class="link" id="feedback_qlogin" href="https://support.qq.com/products/77942?customInfo=.appid101481973" target="_blank">意见反馈</a>  </div><div id="authLogin" class="authLogin"><div class="authHeader" id="authHeader"><div class="logo"></div><span class="title">腾讯业务</span></div><div class="authTips"></div><div class="authWording"><span><span>点击头像，确认账号登录</span><span>腾讯业务</span></span></div><div class="authInfo"><a class="face" id="auth_area" tabindex="1" href="javascript:void(0);" draggable="false" hidefocus="true"><img id="auth_face"> <span id="auth_mengban" class=""></span> <span class="uin" id="auth_uin"></span> <span class="img_out_focus"></span> <span class="nick" id="auth_nick"></span></a></div><div class="cancleAuthOuter" id="cancleAuthOuter"><a id="cancleAuth" class="cancleAuth">使用其他账号</a></div><div class="bottom">  </div></div><div id="newVcodeArea" class="newVcodeArea"><div style="margin: 0px; padding: 0px; line-height: 40px;"><div style="border-bottom: 1px solid #d7d7d7;"><div style="position: absolute; margin-left: 10px"><a href="javascript:pt.plogin.hideVC();" style="color: #007aff; text-decoration: none;">返回</a></div><div style="width: 100%; text-align: center; font-size: 16px; font-weight: bold">安全验证</div></div></div><div id="newVcodeIframe" style="background: none rgb(255, 255, 255); position: absolute; width: 300px; left: 50%; margin-left: -150px; z-index: 9999;"></div></div></div><div id="proxy" class="hide"></div> <script>function cleanCache(f){
	var t=document.createElement("iframe");
	if(f.split("#").length == 3) f = f.substring(0,f.lastIndexOf("#"));
	t.src = f;
	t.style.display = "none";
	document.body.appendChild(t);
};

function isCdnGoResource(src){
        return !!(typeof src === 'string' && src.indexOf('cdn-go.cn/')>-1)
} 

function loadScript(src,successCallback,errorCallback, obj) {
	var tag = document.createElement("script");
	tag.type = 'text/javascript';
    tag.charset="utf-8";
    if(isCdnGoResource(src)){
        tag.crossOrigin = 'anonymous';
    }
    tag.onload = tag.onerror = tag.onreadystatechange = function() {
        tag.onerror = tag.onreadystatechange = null;
    	if (window[obj]) { // 加载成功
            loadJs.onloadTime = +new Date();
            successCallback && successCallback();
    		return;
        }
        if ( !this.readyState ||((this.readyState === "loaded" || this.readyState === "complete")&&!window[obj]) ) {
            errorCallback&&errorCallback(); 
            
        }
    };
	tag.src = src;
	document.getElementsByTagName("head")[0].appendChild(tag);
};
/*===grunt bottom_inc===*/
function ptuiV(v){
    if (v!=window.pt.ptui.ptui_version){
        cleanCache("/clearcache.html#"+location.href);  
    }
}

function checkVersion(){
   
}
/*===grunt bottom_inc===*/

function getJSVersion(){
    //https://pre.cdn-go.cn/qq-web/any.ptlogin2.qq.com/33d4907a
    var frags = "https://qq-web.cdn-go.cn/monorepo/9fce2a54".split('/');
    return frags[frags.length-1] || 'unknown'
}

function isChrome(version){
    return navigator.userAgent.indexOf(' Chrome/'+version+'.')>-1;
}

function loadJs(){
	if(loadJs.hasLoad==true){
		return;
    }

    var jsName = 'c_login_2'
    var jsPath = 'https://qq-web.cdn-go.cn/monorepo/9fce2a54/ptlogin/js/'+jsName+'.js';
    var retryJSPath = 'https://'+location.hostname+'/js/c_login_2.js?v='+getJSVersion()
    var startTime = new Date().getTime();
    var TYPE={
        OK : 0,
        ERROR : 1
    }
    var RET_CODE = {
        OK : 1,
        ERROR: 2, //这个实际没有了
        TIMEOUT : 3, //这个实际也没有了
        TIMEOUT_RETRY_OK : 4,
        TIMEOUT_RETRY_ERROR : 5, //retry也timeout就是网太慢，10s加载js加载不下来
        ERROR_RETRY_OK : 6,
        ERROR_RETRY_ERROR : 7
    }
    var loadJSTimeout = 10000; 
    var isLoadJSTimeout = false;
    var checkKey = 'ptuiCB';
    var loadJSTimer = setTimeout(function(){
        isLoadJSTimeout = true;
        loadScript(retryJSPath, function timeoutRetrySuccessCallback(){
            window.pt007 && window.pt007({
                cgi : jsName,
                type : TYPE.OK,
                httpcode : 200,
                retcode : RET_CODE.TIMEOUT_RETRY_OK,
                cost : new Date().getTime() - startTime
            })
        }, function timeoutRetryFailCallback(){
            window.pt007 && window.pt007({
                cgi : jsName,
                type : TYPE.ERROR,
                httpcode : 200,
                retcode : RET_CODE.TIMEOUT_RETRY_ERROR,
                cost : new Date().getTime() - startTime
            })
        }, checkKey)
    },loadJSTimeout);
	loadJs.hasLoad=true;
    /*===grunt bottom_inc===*/
  
    loadScript(jsPath,function successCallback(){
        clearTimeout(loadJSTimer)
        if(isLoadJSTimeout){
            return;
        }
        //一次成功
        window.pt007 && window.pt007({
            cgi : jsName,
            type : 0,
            httpcode : 200,
            retcode : RET_CODE.OK,
            cost : new Date().getTime() - startTime
        })
   },function errorCallback(){
        clearTimeout(loadJSTimer)
        if(isLoadJSTimeout){
            return;
        }
        loadScript(retryJSPath, function timeoutRetrySuccessCallback(){
            window.pt007 && window.pt007({
                cgi : jsName,
                type : TYPE.OK,
                httpcode : 200,
                retcode : RET_CODE.ERROR_RETRY_OK,
                cost : new Date().getTime() - startTime
            })
        }, function timeoutRetryFailCallback(){
            window.pt007 && window.pt007({
                cgi : jsName,
                type : TYPE.ERROR,
                httpcode : 200,
                retcode : RET_CODE.ERROR_RETRY_ERROR,
                cost : new Date().getTime() - startTime
            })
        },checkKey)
    },checkKey);
    ready();
    /*===grunt bottom_inc===*/
}
function ready() {
    window.setTimeout(checkVersion,1500);
}
document.addEventListener &&
document.addEventListener("DOMContentLoaded", loadJs, false);

if(window.addEventListener){
    var loadEventStart = new Date().getTime();
    console.log('load event bind at ' + loadEventStart);
    window.addEventListener('load', function(){
        var loadEventEnd = new Date().getTime();
        console.log('load event end at ' +loadEventEnd);
        loadJs();
    });
}else{
    window.onload = loadJs;
}


window.setTimeout(loadJs,5000);</script><script src="https://qq-web-other.cdn-go.cn/biz-libs/latest/any.ptlogin2.qq.com/fingerprintjs/index.umd.js" type="text/javascript" defer=""></script><div style="opacity: 0.005; user-select: none; position: absolute; left: 0px; top: 0px;">hiraishinNoJutsuShiki</div></body></html>