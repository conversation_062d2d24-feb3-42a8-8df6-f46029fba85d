/**
 * 使用示例
 * 演示如何使用BuglyLoginAutomation类
 */

const BuglyLoginAutomation = require('./index');

async function example() {
    console.log('=== Bugly登录自动化示例 ===\n');
    
    const automation = new BuglyLoginAutomation();
    
    try {
        // 执行登录流程
        await automation.run();
        
        console.log('\n=== 示例执行完成 ===');
        console.log('请检查生成的文件：');
        console.log('- cookies-*.json (详细Cookie信息)');
        console.log('- cookies-*.txt (<PERSON>ie字符串)');
        console.log('- *.png (过程截图)');
        console.log('- logs/ (日志文件)');
        
    } catch (error) {
        console.error('\n=== 示例执行失败 ===');
        console.error('错误信息:', error.message);
        console.log('\n请检查：');
        console.log('1. config.json 中的账号密码是否正确');
        console.log('2. 网络连接是否正常');
        console.log('3. 查看日志文件获取详细错误信息');
    }
}

// 运行示例
if (require.main === module) {
    example();
}

module.exports = example;
