/**
 * 测试密码登录切换功能
 */

const BuglyLoginAutomation = require('./index');

async function testPasswordLoginSwitch() {
    console.log('=== 测试密码登录切换功能 ===\n');
    
    const automation = new BuglyLoginAutomation();
    
    try {
        // 初始化
        console.log('1. 初始化浏览器...');
        await automation.initialize();
        
        // 导航到登录页面
        console.log('2. 导航到登录页面...');
        await automation.navigateToLogin();
        
        // 保存初始页面截图
        console.log('3. 保存初始页面截图...');
        await automation.takeScreenshot('test-initial-page.png');
        
        // 测试密码登录切换
        console.log('4. 测试密码登录切换...');
        const switchResult = await automation.switchToPasswordLogin();
        
        if (switchResult) {
            console.log('✅ 密码登录切换成功！');
        } else {
            console.log('⚠️ 密码登录切换可能失败，请检查截图');
        }
        
        // 保存切换后的截图
        await automation.takeScreenshot('test-after-switch.png');
        
        // 等待一段时间让用户观察
        console.log('5. 等待5秒钟让您观察页面变化...');
        await automation.page.waitForTimeout(5000);
        
        console.log('\n=== 测试完成 ===');
        console.log('请检查生成的截图文件：');
        console.log('- test-initial-page.png (初始页面)');
        console.log('- test-after-switch.png (切换后页面)');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        
        // 保存错误截图
        try {
            await automation.takeScreenshot('test-error.png');
            console.log('已保存错误截图: test-error.png');
        } catch (screenshotError) {
            console.error('保存错误截图失败:', screenshotError.message);
        }
    } finally {
        // 清理资源
        await automation.cleanup();
    }
}

// 运行测试
if (require.main === module) {
    testPasswordLoginSwitch()
        .then(() => {
            console.log('\n测试程序执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n测试程序执行失败:', error);
            process.exit(1);
        });
}

module.exports = testPasswordLoginSwitch;
