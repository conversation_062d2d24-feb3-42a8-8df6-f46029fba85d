const { chromium } = require('@playwright/test');
const ConfigManager = require('./config');
const Logger = require('./logger');
const RetryHelper = require('./retry');

/**
 * Bugly登录自动化类
 */
class BuglyLoginAutomation {
    constructor() {
        this.configManager = new ConfigManager();
        this.logger = new Logger();
        this.browser = null;
        this.page = null;
        this.config = null;
    }

    /**
     * 初始化浏览器和页面
     */
    async initialize() {
        try {
            console.log('正在初始化配置...');
            this.config = this.configManager.loadConfig();
            
            console.log('正在启动浏览器...');
            const browserConfig = this.configManager.getBrowserConfig();
            
            this.browser = await chromium.launch({
                headless: browserConfig.headless,
                timeout: browserConfig.timeout
            });

            this.page = await this.browser.newPage({
                viewport: browserConfig.viewport
            });

            // 设置页面超时
            this.page.setDefaultTimeout(browserConfig.timeout);
            
            console.log('浏览器初始化完成');
            return true;
        } catch (error) {
            console.error('初始化失败:', error.message);
            await this.cleanup();
            throw error;
        }
    }

    /**
     * 导航到登录页面
     */
    async navigateToLogin() {
        return await RetryHelper.executeWithRetry(async () => {
            const urls = this.configManager.getUrls();
            this.logger.info('正在导航到登录页面...', { url: urls.loginUrl });

            await this.page.goto(urls.loginUrl, {
                waitUntil: 'networkidle',
                timeout: 30000
            });

            this.logger.info('成功导航到登录页面');

            // 等待页面加载完成
            await this.page.waitForLoadState('domcontentloaded');

            return true;
        }, {
            maxRetries: 3,
            delay: 2000,
            retryCondition: RetryHelper.defaultRetryCondition,
            onRetry: (error, attempt) => {
                this.logger.warn(`导航重试 ${attempt}/3`, { error: error.message });
            }
        });
    }

    /**
     * 等待元素出现
     * @param {string} selector 元素选择器
     * @param {number} timeout 超时时间（毫秒）
     */
    async waitForElement(selector, timeout = 10000) {
        try {
            await this.page.waitForSelector(selector, { timeout });
            return true;
        } catch (error) {
            console.error(`等待元素 ${selector} 超时:`, error.message);
            return false;
        }
    }

    /**
     * 截图保存（用于调试）
     * @param {string} filename 文件名
     */
    async takeScreenshot(filename = 'screenshot.png') {
        try {
            await this.page.screenshot({ 
                path: filename, 
                fullPage: true 
            });
            console.log(`截图已保存: ${filename}`);
        } catch (error) {
            console.error('截图失败:', error.message);
        }
    }

    /**
     * 获取页面标题
     */
    async getPageTitle() {
        try {
            const title = await this.page.title();
            console.log('当前页面标题:', title);
            return title;
        } catch (error) {
            console.error('获取页面标题失败:', error.message);
            return null;
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            console.log('资源清理完成');
        } catch (error) {
            console.error('清理资源时出错:', error.message);
        }
    }

    /**
     * 切换到密码登录模式
     */
    async switchToPasswordLogin() {
        try {
            this.logger.info('正在切换到密码登录模式...');

            // 等待页面加载完成
            await this.page.waitForLoadState('networkidle');
            await this.page.waitForTimeout(3000); // 额外等待确保页面完全加载

            // 首先尝试精确的ID选择器（根据您提供的HTML）
            const primarySelector = '#switcher_plogin';

            // 备用选择器
            const fallbackSelectors = [
                'a[id="switcher_plogin"]',
                'a.link[hidefocus="true"]',
                'a:has-text("密码登录")',
                'a[href="javascript:void(0);"]',
                'a[tabindex="8"]',
                '.login_type_pwd',
                'a[onclick*="password"]'
            ];

            let switched = false;

            // 首先尝试主要选择器
            try {
                this.logger.info(`尝试主要选择器: ${primarySelector}`);
                const element = await this.page.waitForSelector(primarySelector, { timeout: 5000 });

                if (element && await element.isVisible()) {
                    await element.click();
                    this.logger.info(`成功点击密码登录按钮: ${primarySelector}`);
                    switched = true;
                } else {
                    this.logger.warn(`元素不可见: ${primarySelector}`);
                }
            } catch (error) {
                this.logger.warn(`主要选择器失败: ${error.message}`);
            }

            // 如果主要选择器失败，尝试备用选择器
            if (!switched) {
                this.logger.info('尝试备用选择器...');
                for (const selector of fallbackSelectors) {
                    try {
                        const element = await this.page.$(selector);
                        if (element && await element.isVisible()) {
                            // 检查元素文本是否包含"密码登录"
                            const text = await element.textContent();
                            if (text && text.includes('密码登录')) {
                                await element.click();
                                this.logger.info(`成功点击密码登录按钮: ${selector}`);
                                switched = true;
                                break;
                            }
                        }
                    } catch (error) {
                        this.logger.debug(`尝试选择器 ${selector} 失败: ${error.message}`);
                    }
                }
            }

            if (!switched) {
                // 尝试通过文本内容查找
                this.logger.info('尝试通过文本内容查找密码登录链接...');
                try {
                    const passwordLoginLink = await this.page.locator('text=密码登录').first();
                    if (await passwordLoginLink.isVisible()) {
                        await passwordLoginLink.click();
                        this.logger.info('通过文本内容成功点击密码登录');
                        switched = true;
                    }
                } catch (error) {
                    this.logger.warn(`通过文本查找失败: ${error.message}`);
                }
            }

            if (!switched) {
                this.logger.warn('未找到密码登录按钮，可能已经在密码登录模式或页面结构有变化');
                // 保存当前页面截图用于调试
                await this.takeScreenshot('no-password-login-found.png');
            }

            // 等待切换完成
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('after-switch-to-password.png');

            return switched;
        } catch (error) {
            this.logger.error('切换到密码登录模式失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 填写登录表单
     */
    async fillLoginForm() {
        try {
            this.logger.info('正在填写登录表单...');

            const loginInfo = this.configManager.getLoginInfo();
            this.logger.info('准备填写账号', { username: loginInfo.username });

            // 等待表单元素加载
            await this.page.waitForTimeout(2000);

            // 用户名输入框的可能选择器（按优先级排序）
            const usernameSelectors = [
                'input[name="u"]',              // QQ登录常用
                'input[id="u"]',                // QQ登录常用
                'input[name="username"]',
                'input[name="account"]',
                'input[placeholder*="QQ号"]',
                'input[placeholder*="账号"]',
                'input[placeholder*="手机号"]',
                'input[type="text"]:visible',
                'input[type="email"]:visible'
            ];

            // 密码输入框的可能选择器（按优先级排序）
            const passwordSelectors = [
                'input[name="p"]',              // QQ登录常用
                'input[id="p"]',                // QQ登录常用
                'input[name="password"]',
                'input[type="password"]:visible',
                'input[placeholder*="密码"]'
            ];

            // 填写用户名
            let usernameFilled = false;
            this.logger.info('开始填写用户名...');

            for (const selector of usernameSelectors) {
                try {
                    // 等待元素出现
                    await this.page.waitForSelector(selector, { timeout: 3000 });
                    const element = await this.page.$(selector);

                    if (element && await element.isVisible()) {
                        // 清空并填写
                        await element.click(); // 先点击获得焦点
                        await element.fill(''); // 清空
                        await this.page.waitForTimeout(500);
                        await element.type(loginInfo.username, { delay: 100 }); // 模拟真实输入

                        // 验证输入是否成功
                        const value = await element.inputValue();
                        if (value === loginInfo.username) {
                            this.logger.info(`成功填写用户名: ${selector}`);
                            usernameFilled = true;
                            break;
                        } else {
                            this.logger.warn(`用户名填写验证失败: ${selector}, 期望: ${loginInfo.username}, 实际: ${value}`);
                        }
                    }
                } catch (error) {
                    this.logger.debug(`尝试用户名选择器 ${selector} 失败: ${error.message}`);
                }
            }

            if (!usernameFilled) {
                await this.takeScreenshot('username-input-not-found.png');
                throw new Error('未找到用户名输入框或填写失败');
            }

            // 填写密码
            let passwordFilled = false;
            this.logger.info('开始填写密码...');

            for (const selector of passwordSelectors) {
                try {
                    // 等待元素出现
                    await this.page.waitForSelector(selector, { timeout: 3000 });
                    const element = await this.page.$(selector);

                    if (element && await element.isVisible()) {
                        // 清空并填写
                        await element.click(); // 先点击获得焦点
                        await element.fill(''); // 清空
                        await this.page.waitForTimeout(500);
                        await element.type(loginInfo.password, { delay: 100 }); // 模拟真实输入

                        // 验证输入是否成功（密码框通常不能读取值，所以只检查是否有内容）
                        const value = await element.inputValue();
                        if (value && value.length > 0) {
                            this.logger.info(`成功填写密码: ${selector}`);
                            passwordFilled = true;
                            break;
                        } else {
                            this.logger.warn(`密码填写验证失败: ${selector}`);
                        }
                    }
                } catch (error) {
                    this.logger.debug(`尝试密码选择器 ${selector} 失败: ${error.message}`);
                }
            }

            if (!passwordFilled) {
                await this.takeScreenshot('password-input-not-found.png');
                throw new Error('未找到密码输入框或填写失败');
            }

            // 等待一下确保输入完成
            await this.page.waitForTimeout(1000);
            await this.takeScreenshot('form-filled.png');
            this.logger.info('登录表单填写完成');

            return true;
        } catch (error) {
            this.logger.error('填写登录表单失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 提交登录表单
     */
    async submitLogin() {
        try {
            console.log('正在提交登录表单...');

            // 登录按钮的可能选择器
            const loginButtonSelectors = [
                'input[type="submit"]',
                'button[type="submit"]',
                'input[value*="登录"]',
                'button:has-text("登录")',
                'a:has-text("登录")',
                '#login_button',
                '.login_button',
                'input[id="loginBtn"]'
            ];

            let submitted = false;
            for (const selector of loginButtonSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element && await element.isVisible()) {
                        await element.click();
                        console.log(`成功点击登录按钮: ${selector}`);
                        submitted = true;
                        break;
                    }
                } catch (error) {
                    console.log(`尝试登录按钮选择器 ${selector} 失败:`, error.message);
                }
            }

            if (!submitted) {
                // 尝试按回车键提交
                await this.page.keyboard.press('Enter');
                console.log('尝试使用回车键提交表单');
            }

            // 等待页面响应
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('after-login-submit.png');

            return true;
        } catch (error) {
            console.error('提交登录表单失败:', error.message);
            throw error;
        }
    }

    /**
     * 检查登录状态
     */
    async checkLoginStatus() {
        try {
            console.log('正在检查登录状态...');

            // 等待页面加载
            await this.page.waitForLoadState('networkidle');

            const currentUrl = this.page.url();
            console.log('当前页面URL:', currentUrl);

            // 检查是否跳转到了Bugly页面
            if (currentUrl.includes('bugly.qq.com')) {
                console.log('登录成功！已跳转到Bugly页面');
                return true;
            }

            // 检查是否还在登录页面
            if (currentUrl.includes('graph.qq.com') || currentUrl.includes('cas.bugly.qq.com')) {
                console.log('可能还在登录流程中，检查是否有错误信息...');

                // 检查错误信息
                const errorSelectors = [
                    '.error',
                    '.err_msg',
                    '[class*="error"]',
                    '[id*="error"]'
                ];

                for (const selector of errorSelectors) {
                    try {
                        const errorElement = await this.page.$(selector);
                        if (errorElement && await errorElement.isVisible()) {
                            const errorText = await errorElement.textContent();
                            console.log('发现错误信息:', errorText);
                        }
                    } catch (error) {
                        // 忽略查找错误信息时的异常
                    }
                }

                return false;
            }

            console.log('登录状态未知，当前URL:', currentUrl);
            return false;

        } catch (error) {
            console.error('检查登录状态失败:', error.message);
            return false;
        }
    }

    /**
     * 提取并输出Cookie
     */
    async extractAndOutputCookies() {
        try {
            console.log('正在提取Cookie...');

            // 获取所有Cookie
            const cookies = await this.page.context().cookies();

            if (cookies.length === 0) {
                console.log('未找到任何Cookie');
                return;
            }

            console.log(`\n========== Cookie信息 ==========`);
            console.log(`总共找到 ${cookies.length} 个Cookie\n`);

            // 按域名分组显示Cookie
            const cookiesByDomain = {};
            cookies.forEach(cookie => {
                const domain = cookie.domain;
                if (!cookiesByDomain[domain]) {
                    cookiesByDomain[domain] = [];
                }
                cookiesByDomain[domain].push(cookie);
            });

            // 输出详细的Cookie信息
            for (const [domain, domainCookies] of Object.entries(cookiesByDomain)) {
                console.log(`\n--- 域名: ${domain} ---`);
                domainCookies.forEach((cookie, index) => {
                    console.log(`${index + 1}. ${cookie.name} = ${cookie.value}`);
                    if (cookie.expires && cookie.expires > 0) {
                        const expireDate = new Date(cookie.expires * 1000);
                        console.log(`   过期时间: ${expireDate.toLocaleString()}`);
                    }
                    console.log(`   路径: ${cookie.path}`);
                    console.log(`   安全: ${cookie.secure ? '是' : '否'}`);
                    console.log(`   HttpOnly: ${cookie.httpOnly ? '是' : '否'}`);
                    console.log('');
                });
            }

            // 生成Cookie字符串格式
            console.log('\n========== Cookie字符串格式 ==========');
            const cookieString = cookies
                .map(cookie => `${cookie.name}=${cookie.value}`)
                .join('; ');
            console.log(cookieString);

            // 生成curl格式的Cookie
            console.log('\n========== Curl格式 ==========');
            console.log(`curl -H "Cookie: ${cookieString}" [URL]`);

            // 保存Cookie到文件
            await this.saveCookiesToFile(cookies, cookieString);

            // 筛选重要的Cookie（通常包含认证信息）
            const importantCookies = cookies.filter(cookie =>
                cookie.name.toLowerCase().includes('session') ||
                cookie.name.toLowerCase().includes('token') ||
                cookie.name.toLowerCase().includes('auth') ||
                cookie.name.toLowerCase().includes('login') ||
                cookie.name.toLowerCase().includes('skey') ||
                cookie.name.toLowerCase().includes('uin') ||
                cookie.domain.includes('qq.com') ||
                cookie.domain.includes('bugly')
            );

            if (importantCookies.length > 0) {
                console.log('\n========== 重要Cookie ==========');
                importantCookies.forEach(cookie => {
                    console.log(`${cookie.name}=${cookie.value} (域名: ${cookie.domain})`);
                });
            }

        } catch (error) {
            console.error('提取Cookie失败:', error.message);
        }
    }

    /**
     * 保存Cookie到文件
     * @param {Array} cookies Cookie数组
     * @param {string} cookieString Cookie字符串
     */
    async saveCookiesToFile(cookies, cookieString) {
        try {
            const fs = require('fs');
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

            // 保存详细的Cookie信息（JSON格式）
            const cookieData = {
                timestamp: new Date().toISOString(),
                url: this.page.url(),
                cookies: cookies,
                cookieString: cookieString
            };

            const jsonFilename = `cookies-${timestamp}.json`;
            fs.writeFileSync(jsonFilename, JSON.stringify(cookieData, null, 2), 'utf8');
            console.log(`\nCookie详细信息已保存到: ${jsonFilename}`);

            // 保存Cookie字符串（文本格式）
            const txtFilename = `cookies-${timestamp}.txt`;
            fs.writeFileSync(txtFilename, cookieString, 'utf8');
            console.log(`Cookie字符串已保存到: ${txtFilename}`);

        } catch (error) {
            console.error('保存Cookie到文件失败:', error.message);
        }
    }

    /**
     * 主执行方法
     */
    async run() {
        const startTime = Date.now();
        this.logger.info('开始执行Bugly登录自动化流程');

        try {
            // 初始化
            this.logger.info('步骤 1/7: 初始化浏览器和配置');
            await this.initialize();

            // 导航到登录页面
            this.logger.info('步骤 2/7: 导航到登录页面');
            await this.navigateToLogin();
            await this.getPageTitle();

            // 保存初始页面截图
            this.logger.info('步骤 3/7: 保存初始页面截图');
            await this.takeScreenshot('login-page-initial.png');

            // 切换到密码登录模式
            this.logger.info('步骤 4/7: 切换到密码登录模式');
            const switchSuccess = await this.switchToPasswordLogin();

            if (!switchSuccess) {
                this.logger.warn('密码登录切换可能失败，但继续尝试填写表单');
            }

            // 填写登录表单
            this.logger.info('步骤 5/7: 填写登录表单');
            await this.fillLoginForm();

            // 提交登录表单
            this.logger.info('步骤 6/7: 提交登录表单');
            await this.submitLogin();

            // 检查登录状态并提取Cookie
            this.logger.info('步骤 7/7: 检查登录状态并提取Cookie');
            const loginSuccess = await this.checkLoginStatus();

            if (loginSuccess) {
                this.logger.info('登录成功！开始提取Cookie...');
                await this.extractAndOutputCookies();

                const duration = Date.now() - startTime;
                this.logger.info(`登录流程完成，总耗时: ${duration}ms`);
                console.log('\n✅ 登录成功！Cookie已提取并保存。');

            } else {
                this.logger.warn('登录可能失败');
                console.log('\n❌ 登录失败，请检查以下可能的原因：');
                console.log('1. 账号密码是否正确');
                console.log('2. 是否需要验证码验证');
                console.log('3. 账号是否被限制登录');
                console.log('4. 网络连接是否正常');
                console.log('5. QQ登录页面是否有变化');

                // 保存失败时的截图
                await this.takeScreenshot('login-failed.png');
                this.logger.info('已保存失败截图: login-failed.png');
            }

        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('执行过程中发生错误', {
                error: error.message,
                stack: error.stack,
                duration: duration
            });

            console.log('\n❌ 程序执行失败：', error.message);

            // 保存错误时的截图
            try {
                await this.takeScreenshot('error-screenshot.png');
                this.logger.info('已保存错误截图: error-screenshot.png');
            } catch (screenshotError) {
                this.logger.error('保存错误截图失败', { error: screenshotError.message });
            }

            throw error;
        } finally {
            await this.cleanup();
        }
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    const automation = new BuglyLoginAutomation();
    automation.run()
        .then(() => {
            console.log('程序执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('程序执行失败:', error);
            process.exit(1);
        });
}

module.exports = BuglyLoginAutomation;
