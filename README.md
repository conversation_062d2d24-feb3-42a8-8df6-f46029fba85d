# Bugly登录自动化工具

这是一个使用Playwright自动化登录QQ并获取Bugly Cookie的工具。

## 功能特性

- 🚀 自动化QQ登录流程
- 🔄 智能重试机制
- 📝 详细日志记录
- 🍪 Cookie自动提取和保存
- 📸 过程截图保存
- ⚙️ 灵活的配置管理

## 安装和使用

### 1. 安装依赖

```bash
npm install
```

### 2. 配置登录信息

编辑 `config.json` 文件，填入你的QQ账号和密码：

```json
{
  "login": {
    "username": "你的QQ号",
    "password": "你的QQ密码"
  }
}
```

### 3. 运行程序

```bash
npm start
```

或者直接运行：

```bash
node index.js
```

## 配置说明

### config.json 配置文件

```json
{
  "login": {
    "username": "your_qq_username",    // QQ账号
    "password": "your_qq_password"     // QQ密码
  },
  "browser": {
    "headless": false,                 // 是否无头模式运行
    "timeout": 30000,                  // 超时时间（毫秒）
    "viewport": {
      "width": 1280,                   // 浏览器窗口宽度
      "height": 720                    // 浏览器窗口高度
    }
  },
  "urls": {
    "loginUrl": "..."                  // 登录页面URL（已预配置）
  }
}
```

## 输出文件

程序运行后会生成以下文件：

### Cookie文件
- `cookies-[时间戳].json` - 详细的Cookie信息（JSON格式）
- `cookies-[时间戳].txt` - Cookie字符串（可直接用于HTTP请求）

### 截图文件
- `login-page-initial.png` - 初始登录页面
- `after-switch-to-password.png` - 切换到密码登录后
- `form-filled.png` - 表单填写完成后
- `after-login-submit.png` - 提交登录后
- `login-failed.png` - 登录失败时（如果失败）
- `error-screenshot.png` - 发生错误时（如果有错误）

### 日志文件
- `logs/bugly-login-[日期].log` - 详细的执行日志

## 使用提取的Cookie

### 方法1：直接复制Cookie字符串
从 `cookies-[时间戳].txt` 文件中复制Cookie字符串，在HTTP请求中使用：

```bash
curl -H "Cookie: [复制的Cookie字符串]" https://bugly.qq.com/v2/
```

### 方法2：使用JSON格式的Cookie
从 `cookies-[时间戳].json` 文件中获取详细的Cookie信息，可以在代码中使用。

## 故障排除

### 常见问题

1. **登录失败**
   - 检查账号密码是否正确
   - 确认账号没有被限制登录
   - 检查是否需要验证码验证

2. **页面加载超时**
   - 检查网络连接
   - 增加 `config.json` 中的 `timeout` 值

3. **找不到页面元素**
   - QQ登录页面可能有更新
   - 查看截图文件确认页面状态
   - 检查日志文件获取详细错误信息

### 调试模式

设置 `config.json` 中的 `headless: false` 可以看到浏览器操作过程，便于调试。

## 注意事项

⚠️ **安全提醒**
- 请妥善保管你的账号密码
- 不要将包含真实账号密码的配置文件提交到版本控制系统
- Cookie信息包含敏感数据，请注意保护

⚠️ **使用限制**
- 本工具仅用于学习和测试目的
- 请遵守相关服务的使用条款
- 频繁的自动化登录可能触发安全机制

## 技术架构

- **Playwright**: 浏览器自动化框架
- **Node.js**: 运行环境
- **模块化设计**: 配置管理、日志记录、重试机制分离

## 许可证

MIT License
