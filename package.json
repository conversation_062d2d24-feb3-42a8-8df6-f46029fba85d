{"name": "bugly-login-automation", "version": "1.0.0", "description": "使用Playwright自动化登录Bugly并获取Cookie", "main": "index.js", "scripts": {"start": "node index.js", "test": "node quick-test.js", "test-local": "node test-local.js", "test-password": "node test-password-login.js", "example": "node example.js"}, "keywords": ["playwright", "automation", "bugly", "login", "cookie"], "author": "", "license": "MIT", "dependencies": {"@playwright/test": "^1.40.0"}, "devDependencies": {}}